package ac;

import android.view.View;
import androidx.sqlite.SQLiteConnection;
import com.offline.bible.dao.quiz.QuizDailyDao_Impl;
import com.offline.bible.ui.home.aiverse.ui.AiVerseJarMainActivity;

/* compiled from: R8$$SyntheticClass */
/* loaded from: classes4.dex */
public final /* synthetic */ class c0 implements th.l {
    public final /* synthetic */ int a;

    public /* synthetic */ c0(int i) {
        this.a = i;
    }

    @Override // th.l
    public final Object invoke(Object obj) {
        switch (this.a) {
            case 0:
                View it = (View) obj;
                int i = AiVerseJarMainActivity.n;
                kotlin.jvm.internal.q.g(it, "it");
                return Boolean.valueOf(it.isSelected());
            case 1:
                return QuizDailyDao_Impl.lambda$getAllQuizDailyLogMode$4((SQLiteConnection) obj);
            default:
                kotlin.jvm.internal.q.g((Throwable) obj, "it");
                return fh.g0.a;
        }
    }
}
