package ac;

import androidx.room.RoomDatabase;
import androidx.work.impl.WorkManagerImpl;
import com.offline.bible.compose.BibleComposeBaseActivity;
import com.offline.bible.ui.home.aiverse.ui.VerseOfYouActivity;
import ub.z$a;

/* compiled from: R8$$SyntheticClass */
/* loaded from: classes4.dex */
public final /* synthetic */ class e1 implements th.a {
    public final /* synthetic */ int a;
    public final /* synthetic */ Object b;

    public /* synthetic */ e1(Object obj, int i) {
        this.a = i;
        this.b = obj;
    }

    @Override // th.a
    public final Object invoke() {
        Object obj = this.b;
        switch (this.a) {
            case 0:
                int i = VerseOfYouActivity.u;
                ((VerseOfYouActivity) obj).A();
                return fh.g0.a;
            case 1:
                return RoomDatabase.runInTransaction$lambda$10((Runnable) obj);
            case 2:
                return ((WorkManagerImpl) obj).lambda$rescheduleEligibleWork$0();
            case 3:
                return ((BibleComposeBaseActivity) obj).getDefaultViewModelCreationExtras();
            default:
                ((z$a) obj).g();
                return null;
        }
    }
}
