package ac;

import android.view.MotionEvent;
import android.view.View;
import android.view.View$OnTouchListener;
import com.offline.bible.ui.home.aiverse.ui.AiVerseJarMainActivity;

/* compiled from: R8$$SyntheticClass */
/* loaded from: classes4.dex */
public final /* synthetic */ class b0 implements View$OnTouchListener {
    @Override // android.view.View$OnTouchListener
    public final boolean onTouch(View view, MotionEvent motionEvent) {
        int i = AiVerseJarMainActivity.n;
        if (motionEvent.getAction() != 0) {
            return false;
        }
        view.getParent().requestDisallowInterceptTouchEvent(true);
        return false;
    }
}
