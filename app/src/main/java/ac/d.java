package ac;

import android.content.Intent;
import android.os.Bundle;
import android.view.LifecycleCoroutineScope;
import android.view.LifecycleOwnerKt;
import android.view.View;
import android.view.View$OnClickListener;
import android.widget.TextView;
import androidx.browser.trusted.sharing.ShareTarget;
import androidx.constraintlayout.core.motion.utils.TypedValues$TransitionType;
import androidx.core.view.ViewGroupKt;
import com.bible.holy.bible.for.women.R$string;
import com.offline.bible.dao.aiverse.AiVerseModel;
import com.offline.bible.entity.Config;
import com.offline.bible.entity.help.HelpCenterItem;
import com.offline.bible.ui.EncouragePray2Activity;
import com.offline.bible.ui.aigc.view.WritePrayerActivity;
import com.offline.bible.ui.community.CommunityInfoMoreDialog;
import com.offline.bible.ui.dialog.NHMSortBySelectDialog;
import com.offline.bible.ui.dialog.TriviaAdDialog;
import com.offline.bible.ui.help.HelpCenterDetailActivity;
import com.offline.bible.ui.home.aiverse.ui.AiVerseDetailActivity;
import com.offline.bible.ui.home.aiverse.ui.AiVerseJarMainActivity;
import com.offline.bible.ui.me.MultiEditionActivity;
import com.offline.bible.ui.more.ThemeSettingActivity;
import com.offline.bible.ui.overlay.OverlayWindowActivity;
import com.offline.bible.ui.read.BibleReadDialog;
import com.offline.bible.ui.read.explore.ExploreCDialog;
import com.offline.bible.ui.read.readIndex.ReadIndexActivity;
import com.offline.bible.ui.removead.RemoveAdActivity;
import com.offline.bible.ui.survey.SurveyNewFunctionActivity;
import com.offline.bible.utils.SPUtil;
import com.offline.bible.utils.ToastUtil;
import com.offline.bible.views.FlowLayout;
import com.offline.bible.views.businessview.homev5.HomeTaskQuizLayout;
import com.offline.bible.views.businessview.homev5.IHomeTaskQuiz$OnQuizFinishClick;
import id.Jx.DripSqLpiE;
import nk.u1;
import ua.k7;

/* compiled from: R8$$SyntheticClass */
/* loaded from: classes4.dex */
public final /* synthetic */ class d implements View$OnClickListener {
    public final /* synthetic */ int a;
    public final /* synthetic */ Object b;

    public /* synthetic */ d(Object obj, int i) {
        this.a = i;
        this.b = obj;
    }

    @Override // android.view.View$OnClickListener
    public final void onClick(View view) {
        Object obj = this.b;
        switch (this.a) {
            case 0:
                AiVerseDetailActivity aiVerseDetailActivity = (AiVerseDetailActivity) obj;
                if (aiVerseDetailActivity.r == null) {
                    aiVerseDetailActivity.G();
                    String str = aiVerseDetailActivity.p;
                    LifecycleCoroutineScope lifecycleScope = LifecycleOwnerKt.getLifecycleScope(aiVerseDetailActivity);
                    vk.c cVar = nk.v0.a;
                    u1.c(lifecycleScope, vk.b.b, null, new k(str, aiVerseDetailActivity, null), 2);
                }
                AiVerseModel aiVerseModel = aiVerseDetailActivity.r;
                if (aiVerseModel == null || aiVerseModel.get_id() == null) {
                    return;
                }
                aiVerseDetailActivity.G();
                LifecycleCoroutineScope lifecycleScope2 = LifecycleOwnerKt.getLifecycleScope(aiVerseDetailActivity);
                vk.c cVar2 = nk.v0.a;
                u1.c(lifecycleScope2, vk.b.b, null, new g(aiVerseDetailActivity, aiVerseModel, null), 2);
                return;
            case 1:
                int i = AiVerseJarMainActivity.n;
                AiVerseJarMainActivity aiVerseJarMainActivity = (AiVerseJarMainActivity) obj;
                String string = aiVerseJarMainActivity.getString(R$string.arj);
                kotlin.jvm.internal.q.f(string, "getString(...)");
                aiVerseJarMainActivity.z(string);
                return;
            case 2:
                ((ExploreCDialog) obj).dismiss();
                return;
            case 3:
                int i2 = EncouragePray2Activity.r;
                EncouragePray2Activity encouragePray2Activity = (EncouragePray2Activity) obj;
                encouragePray2Activity.getClass();
                m9.c.c().f("Pray_encourage_back");
                encouragePray2Activity.setResult(-1);
                encouragePray2Activity.finish();
                return;
            case 4:
                int i3 = ReadIndexActivity.K;
                ((ReadIndexActivity) obj).finish();
                return;
            case 5:
                int i4 = RemoveAdActivity.o;
                RemoveAdActivity removeAdActivity = (RemoveAdActivity) obj;
                removeAdActivity.startActivity(new Intent(removeAdActivity, (Class<?>) MultiEditionActivity.class));
                return;
            case 6:
                HomeTaskQuizLayout.lambda$updateMoreQuizLayout$6((IHomeTaskQuiz$OnQuizFinishClick) obj, view);
                return;
            case 7:
                int i5 = SurveyNewFunctionActivity.k;
                SurveyNewFunctionActivity surveyNewFunctionActivity = (SurveyNewFunctionActivity) obj;
                o9.s sVar = new o9.s("/api/surveys/answer/", ShareTarget.METHOD_POST);
                k7 k7Var = surveyNewFunctionActivity.j;
                if (k7Var == null) {
                    kotlin.jvm.internal.q.o("mLayoutBinding");
                    throw null;
                }
                sVar.answer1 = k7Var.a.getText().toString();
                k7 k7Var2 = surveyNewFunctionActivity.j;
                if (k7Var2 == null) {
                    kotlin.jvm.internal.q.o("mLayoutBinding");
                    throw null;
                }
                sVar.answer2 = k7Var2.b.getText().toString();
                k7 k7Var3 = surveyNewFunctionActivity.j;
                if (k7Var3 == null) {
                    kotlin.jvm.internal.q.o("mLayoutBinding");
                    throw null;
                }
                sVar.answer3 = k7Var3.c.getText().toString();
                surveyNewFunctionActivity.c.m(sVar, new ld.a(surveyNewFunctionActivity));
                return;
            case 8:
                int i6 = WritePrayerActivity.r;
                ((WritePrayerActivity) obj).C();
                return;
            case 9:
                ThemeSettingActivity themeSettingActivity = (ThemeSettingActivity) obj;
                Config config = themeSettingActivity.k;
                if (config == null) {
                    kotlin.jvm.internal.q.o("mConfig");
                    throw null;
                }
                config.g(2);
                SPUtil instant = SPUtil.getInstant();
                Config config2 = themeSettingActivity.k;
                if (config2 == null) {
                    kotlin.jvm.internal.q.o("mConfig");
                    throw null;
                }
                instant.save("read_config", x.i.e(config2));
                SPUtil.getInstant().save("night_mode_auto_by_system", 0);
                themeSettingActivity.o();
                themeSettingActivity.n();
                m9.c.c().g("More_theme", "2");
                m9.c.c().f("Darkmode_switch");
                return;
            case 10:
                int i7 = OverlayWindowActivity.f;
                ((OverlayWindowActivity) obj).finish();
                return;
            case 11:
                ((CommunityInfoMoreDialog) obj).dismiss();
                return;
            case 12:
                NHMSortBySelectDialog nHMSortBySelectDialog = (NHMSortBySelectDialog) obj;
                nHMSortBySelectDialog.b = 2;
                nHMSortBySelectDialog.h();
                nHMSortBySelectDialog.dismiss();
                if ("note".equals(nHMSortBySelectDialog.c)) {
                    m9.c.c().f("notes_canonical");
                    return;
                }
                if (DripSqLpiE.kBsHUjLyaQcmBl.equals(nHMSortBySelectDialog.c)) {
                    m9.c.c().f("highlights_canonical");
                    return;
                } else {
                    if ("bookmark".equals(nHMSortBySelectDialog.c)) {
                        m9.c.c().f("bookmarks_canonical");
                        return;
                    }
                    return;
                }
            case 13:
                ((ub.x0) obj).dismiss();
                return;
            case 14:
                StringBuilder sb = new StringBuilder();
                ub.e1 e1Var = (ub.e1) obj;
                FlowLayout flowLayout = e1Var.b().b;
                kotlin.jvm.internal.q.f(flowLayout, "flowLayout");
                for (View view2 : ViewGroupKt.getChildren(flowLayout)) {
                    if (view2.isSelected()) {
                        sb.append(((TextView) view2).getText().toString());
                        sb.append(",");
                    }
                }
                if (sb.length() > 0) {
                    sb.deleteCharAt(sb.length() - 1);
                }
                String string2 = sb.toString();
                kotlin.jvm.internal.q.f(string2, "toString(...)");
                Bundle bundle = new Bundle();
                int i8 = e1Var.a;
                bundle.putString(TypedValues$TransitionType.S_FROM, String.valueOf(i8));
                if (e1Var.c) {
                    bundle.putString("option_like", "like");
                } else if (e1Var.d) {
                    bundle.putString("option_like", "dislike");
                    bundle.putString("option_set", string2);
                }
                bundle.putString("custom_answer", lk.q.A0(e1Var.b().a.getText().toString()).toString());
                m9.c.c().e(bundle, "sabbath_survey_submit_click");
                ToastUtil.showMessage(e1Var.getContext(), e1Var.getContext().getString(R$string.apj));
                if (i8 == 0 || i8 == 1) {
                    SPUtil.getInstant().save("is_feedback_showed3", Boolean.TRUE);
                }
                e1Var.dismiss();
                return;
            case 15:
                TriviaAdDialog triviaAdDialog = (TriviaAdDialog) obj;
                triviaAdDialog.dismiss();
                m9.c.c().g("quiz_redirect_pop_click", String.valueOf(triviaAdDialog.b));
                return;
            case 16:
                int i9 = HelpCenterDetailActivity.p;
                SPUtil instant2 = SPUtil.getInstant();
                StringBuilder sb2 = new StringBuilder("help_yes_no_");
                HelpCenterDetailActivity helpCenterDetailActivity = (HelpCenterDetailActivity) obj;
                HelpCenterItem helpCenterItem = helpCenterDetailActivity.o;
                kotlin.jvm.internal.q.d(helpCenterItem);
                sb2.append(helpCenterItem.getId());
                instant2.save(sb2.toString(), 1);
                helpCenterDetailActivity.z();
                m9.c cVarC = m9.c.c();
                Bundle bundle2 = new Bundle();
                HelpCenterItem helpCenterItem2 = helpCenterDetailActivity.o;
                kotlin.jvm.internal.q.d(helpCenterItem2);
                bundle2.putString("ID", String.valueOf(helpCenterItem2.getId()));
                bundle2.putString("comment", "0");
                fh.g0 g0Var = fh.g0.a;
                cVarC.e(bundle2, "help_center_faq_comment");
                return;
            default:
                ((BibleReadDialog) obj).dismiss();
                return;
        }
    }
}
