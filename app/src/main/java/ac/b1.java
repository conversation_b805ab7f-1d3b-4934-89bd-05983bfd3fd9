package ac;

import android.view.animation.AlphaAnimation;
import android.view.animation.AnimationSet;
import android.view.animation.LinearInterpolator;
import android.view.animation.ScaleAnimation;
import android.view.animation.TranslateAnimation;
import androidx.room.InvalidationTracker;
import com.offline.bible.ui.home.aiverse.adapter.VerseJarMessageAdapter;
import com.offline.bible.ui.home.aiverse.ui.VerseOfYouActivity;
import com.offline.bible.ui.plan14.Plan14Activity;
import com.offline.bible.utils.MetricsUtils;
import ua.k3;

/* compiled from: R8$$SyntheticClass */
/* loaded from: classes4.dex */
public final /* synthetic */ class b1 implements th.a {
    public final /* synthetic */ int a;
    public final /* synthetic */ Object b;

    public /* synthetic */ b1(Object obj, int i) {
        this.a = i;
        this.b = obj;
    }

    @Override // th.a
    public final Object invoke() {
        Object obj = this.b;
        switch (this.a) {
            case 0:
                int i = VerseOfYouActivity.u;
                return new VerseJarMessageAdapter(((VerseOfYouActivity) obj).s);
            case 1:
                return InvalidationTracker.onRefreshCompleted$lambda$1((InvalidationTracker) obj);
            default:
                int i2 = Plan14Activity.p;
                Plan14Activity plan14Activity = (Plan14Activity) obj;
                float fDp2px = (MetricsUtils.dp2px(plan14Activity, 384.0f) - ((x.q.a() - MetricsUtils.dp2px(plan14Activity, 520.0f)) / 2.0f)) / MetricsUtils.dp2px(plan14Activity, 520.0f);
                k3 k3Var = plan14Activity.j;
                if (k3Var == null) {
                    kotlin.jvm.internal.q.o("mLayoutBinding");
                    throw null;
                }
                k3Var.c.clearAnimation();
                ScaleAnimation scaleAnimation = new ScaleAnimation(1.0f, 0.0f, 1.0f, 0.0f, 1, 1.0f, 1, fDp2px < 0.0f ? 0.0f : fDp2px > 1.0f ? 1.0f : fDp2px);
                AlphaAnimation alphaAnimation = new AlphaAnimation(1.0f, 0.0f);
                new TranslateAnimation(0.0f, MetricsUtils.dp2px(plan14Activity, 328.0f), 0.0f, MetricsUtils.dp2px(plan14Activity, 340.0f));
                AnimationSet animationSet = new AnimationSet(true);
                animationSet.addAnimation(scaleAnimation);
                animationSet.addAnimation(alphaAnimation);
                animationSet.setInterpolator(new LinearInterpolator());
                animationSet.setDuration(280L);
                animationSet.setFillAfter(true);
                animationSet.setFillBefore(false);
                animationSet.cancel();
                animationSet.reset();
                animationSet.setAnimationListener(new rc.b(plan14Activity));
                k3 k3Var2 = plan14Activity.j;
                if (k3Var2 == null) {
                    kotlin.jvm.internal.q.o("mLayoutBinding");
                    throw null;
                }
                k3Var2.c.startAnimation(animationSet);
                m9.c.c().f("home_element_popup_close");
                return fh.g0.a;
        }
    }
}
