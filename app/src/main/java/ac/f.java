package ac;

import android.content.Intent;
import android.view.LifecycleCoroutineScope;
import android.view.LifecycleOwnerKt;
import android.view.View;
import android.view.View$OnClickListener;
import androidx.browser.trusted.sharing.ShareTarget;
import androidx.compose.ui.jO.TpeYhFK;
import com.blankj.utilcode.util.ToastUtils;
import com.offline.bible.dao.plan.PartForDayPlan;
import com.offline.bible.dao.plan.PlanDay;
import com.offline.bible.dao.plan.PlanDbManager;
import com.offline.bible.ui.EncouragePray2Activity;
import com.offline.bible.ui.community.CommunityInfoMoreDialog;
import com.offline.bible.ui.dialog.BibleVoiceSelectHKTWDialog;
import com.offline.bible.ui.dialog.NHMSortBySelectDialog;
import com.offline.bible.ui.dialog.PrivacyPolicyDialog;
import com.offline.bible.ui.dialog.PrivacyPolicyDialog$c;
import com.offline.bible.ui.home.aiverse.ui.AiVerseDetailActivity;
import com.offline.bible.ui.more.ThemeSettingActivity;
import com.offline.bible.ui.overlay.OverlayWindowGuide2Activity;
import com.offline.bible.ui.plan.v2.PlanFinishEncourageActivity;
import com.offline.bible.ui.plan.v2.PlanReadActivity;
import com.offline.bible.ui.plan14.Plan14EncourageActivity;
import com.offline.bible.ui.read.GuidedReadFragment;
import com.offline.bible.ui.read.explore.ExploreCDialog;
import com.offline.bible.ui.read.readIndex.ReadIndexActivity;
import com.offline.bible.ui.voice.VoicePlayingActivity;
import com.offline.bible.utils.SPUtil;
import com.offline.bible.views.businessview.homev5.HomeTaskQuizLayoutBigLetter;
import com.offline.bible.views.businessview.homev5.IHomeTaskQuiz$OnQuizFinishClick;
import java.util.ArrayList;
import nk.u1;
import ua.y3;

/* compiled from: R8$$SyntheticClass */
/* loaded from: classes4.dex */
public final /* synthetic */ class f implements View$OnClickListener {
    public final /* synthetic */ int a;
    public final /* synthetic */ Object b;

    public /* synthetic */ f(Object obj, int i) {
        this.a = i;
        this.b = obj;
    }

    @Override // android.view.View$OnClickListener
    public final void onClick(View view) throws IllegalAccessException, SecurityException, IllegalArgumentException {
        PartForDayPlan partForDayPlan;
        String name;
        PartForDayPlan partForDayPlan2;
        PartForDayPlan partForDayPlan3;
        Object obj = this.b;
        switch (this.a) {
            case 0:
                int i = AiVerseDetailActivity.u;
                AiVerseDetailActivity aiVerseDetailActivity = (AiVerseDetailActivity) obj;
                m9.c.c().e(aiVerseDetailActivity.z(aiVerseDetailActivity.r), "ai_verse_down_click");
                aiVerseDetailActivity.d.show();
                LifecycleCoroutineScope lifecycleScope = LifecycleOwnerKt.getLifecycleScope(aiVerseDetailActivity);
                vk.c cVar = nk.v0.a;
                u1.c(lifecycleScope, vk.b.b, null, new h(aiVerseDetailActivity, null), 2);
                return;
            case 1:
                ((ExploreCDialog) obj).dismiss();
                return;
            case 2:
                int i2 = EncouragePray2Activity.r;
                ((EncouragePray2Activity) obj).finish();
                m9.c.c().f("Pray_encourage_return");
                return;
            case 3:
                HomeTaskQuizLayoutBigLetter.lambda$updateFinishLayout$7((IHomeTaskQuiz$OnQuizFinishClick) obj, view);
                return;
            case 4:
                int i3 = ThemeSettingActivity.l;
                ((ThemeSettingActivity) obj).finish();
                return;
            case 5:
                int i4 = OverlayWindowGuide2Activity.j;
                ((OverlayWindowGuide2Activity) obj).onBackPressed();
                return;
            case 6:
                PlanReadActivity planReadActivity = (PlanReadActivity) obj;
                if (planReadActivity.k != null) {
                    PlanDay planDay = planReadActivity.j;
                    String commentary = planDay != null ? planDay.getCommentary() : null;
                    if (commentary == null || commentary.length() == 0 || planReadActivity.l != 0) {
                        int i5 = planReadActivity.l;
                        PlanDay planDay2 = planReadActivity.j;
                        String commentary2 = planDay2 != null ? planDay2.getCommentary() : null;
                        int i6 = i5 - (((commentary2 == null || commentary2.length() == 0) ? 1 : 0) ^ 1);
                        if (i6 >= 0) {
                            ArrayList<PartForDayPlan> arrayList = planReadActivity.k;
                            kotlin.jvm.internal.q.d(arrayList);
                            if (i6 < arrayList.size()) {
                                ArrayList<PartForDayPlan> arrayList2 = planReadActivity.k;
                                kotlin.jvm.internal.q.d(arrayList2);
                                PartForDayPlan partForDayPlan4 = arrayList2.get(i6);
                                kotlin.jvm.internal.q.f(partForDayPlan4, "get(...)");
                                PartForDayPlan partForDayPlan5 = partForDayPlan4;
                                partForDayPlan5.setStatus(1);
                                PlanDbManager.getInstance().savePlanPart(partForDayPlan5).b();
                                ae.j jVar = (ae.j) be.a.b(planReadActivity).get(ae.j.class);
                                int i7 = partForDayPlan5.get_id();
                                jVar.getClass();
                                com.offline.bible.api.request.plan.g gVar = new com.offline.bible.api.request.plan.g("/api/plan_status/", ShareTarget.METHOD_POST);
                                gVar.plan_list_id = i7;
                                gVar.status = 1;
                                jVar.c.l(gVar, n9.d.class, null);
                            }
                        }
                    } else {
                        PlanDay planDay3 = planReadActivity.j;
                        kotlin.jvm.internal.q.d(planDay3);
                        planDay3.setCommentaryStatus(1);
                        PlanDbManager.getInstance().savePlanDay(planReadActivity.j).b();
                    }
                }
                int i8 = planReadActivity.l;
                ArrayList<PartForDayPlan> arrayList3 = planReadActivity.k;
                int size = (arrayList3 != null ? arrayList3.size() : 0) - 1;
                PlanDay planDay4 = planReadActivity.j;
                String commentary3 = planDay4 != null ? planDay4.getCommentary() : null;
                if (i8 >= size + (((commentary3 == null || commentary3.length() == 0) ? 1 : 0) ^ 1)) {
                    Intent intent = new Intent(planReadActivity, (Class<?>) PlanFinishEncourageActivity.class);
                    ArrayList<PartForDayPlan> arrayList4 = planReadActivity.k;
                    Intent intentPutExtra = intent.putExtra(TpeYhFK.gGTETsPT, (arrayList4 == null || (partForDayPlan3 = arrayList4.get(0)) == null) ? 0 : partForDayPlan3.getPlan_id());
                    ArrayList<PartForDayPlan> arrayList5 = planReadActivity.k;
                    if (arrayList5 == null || (partForDayPlan2 = arrayList5.get(0)) == null || (name = partForDayPlan2.getName()) == null) {
                        name = "";
                    }
                    planReadActivity.startActivity(intentPutExtra.putExtra("plan_name", name));
                    planReadActivity.finish();
                } else {
                    planReadActivity.l++;
                    planReadActivity.p();
                    planReadActivity.o();
                    planReadActivity.n();
                    y3 y3Var = planReadActivity.o;
                    if (y3Var == null) {
                        kotlin.jvm.internal.q.o("mDataBindingLayout");
                        throw null;
                    }
                    y3Var.i.scrollToPosition(0);
                }
                ArrayList<PartForDayPlan> arrayList6 = planReadActivity.k;
                if (arrayList6 == null || (partForDayPlan = arrayList6.get(0)) == null) {
                    return;
                }
                m9.c.c().g("NewPlan_Read_Next", String.valueOf(partForDayPlan.getPlan_id()));
                return;
            case 7:
                int i9 = Plan14EncourageActivity.l;
                Plan14EncourageActivity plan14EncourageActivity = (Plan14EncourageActivity) obj;
                plan14EncourageActivity.setResult(-1);
                plan14EncourageActivity.finish();
                return;
            case 8:
                CommunityInfoMoreDialog communityInfoMoreDialog = (CommunityInfoMoreDialog) obj;
                communityInfoMoreDialog.dismiss();
                sb.j jVar2 = communityInfoMoreDialog.b;
                if (jVar2 != null) {
                    jVar2.a.j.h.performClick();
                    return;
                }
                return;
            case 9:
                a2.h hVar = ((BibleVoiceSelectHKTWDialog) obj).c;
                if (hVar != null) {
                    int i10 = VoicePlayingActivity.y;
                    ((BibleVoiceSelectHKTWDialog) hVar.b).dismiss();
                    if (((String) SPUtil.getInstant().get("bible_voice_cn_area_type", "cn_tw")).equals("cn_tw")) {
                        SPUtil.getInstant().save("bible_voice_cn_area_type", "cn_hk");
                        ToastUtils.a("已將朗讀聲音設置為粵語");
                    } else {
                        SPUtil.getInstant().save("bible_voice_cn_area_type", "cn_tw");
                        ToastUtils.a("已將朗讀聲音設置為國語");
                    }
                    com.offline.bible.voice.a.l(com.offline.bible.voice.a.b(), com.offline.bible.voice.a.c());
                    return;
                }
                return;
            case 10:
                NHMSortBySelectDialog nHMSortBySelectDialog = (NHMSortBySelectDialog) obj;
                nHMSortBySelectDialog.dismiss();
                if ("note".equals(nHMSortBySelectDialog.c)) {
                    m9.c.c().f("notes_cancel");
                    return;
                } else if ("highlight".equals(nHMSortBySelectDialog.c)) {
                    m9.c.c().f("highlights_cancel");
                    return;
                } else {
                    if ("bookmark".equals(nHMSortBySelectDialog.c)) {
                        m9.c.c().f("bookmarks_cancel");
                        return;
                    }
                    return;
                }
            case 11:
                PrivacyPolicyDialog$c privacyPolicyDialog$c = ((PrivacyPolicyDialog) obj).b;
                if (privacyPolicyDialog$c != null) {
                    privacyPolicyDialog$c.a();
                    return;
                }
                return;
            default:
                m9.c.c().f("bible_read_guide_menu_click");
                GuidedReadFragment guidedReadFragment = (GuidedReadFragment) obj;
                guidedReadFragment.startActivity(new Intent(guidedReadFragment.c, (Class<?>) ReadIndexActivity.class));
                return;
        }
    }
}
