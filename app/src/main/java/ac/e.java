package ac;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.view.View$OnClickListener;
import androidx.core.view.accessibility.AccessibilityEventCompat;
import com.offline.bible.App;
import com.offline.bible.dao.plan.PartForDayPlan;
import com.offline.bible.entity.Config;
import com.offline.bible.entity.ShareContentBean;
import com.offline.bible.entity.help.HelpCenterItem;
import com.offline.bible.ui.MainActivity;
import com.offline.bible.ui.community.CommunityInfoMoreDialog;
import com.offline.bible.ui.dialog.BibleVoiceSelectHKTWDialog;
import com.offline.bible.ui.dialog.CommonTitleMessageDialog;
import com.offline.bible.ui.dialog.NHMSortBySelectDialog;
import com.offline.bible.ui.dialog.UnlockDiscoverQuizDialog;
import com.offline.bible.ui.help.HelpCenterDetailActivity;
import com.offline.bible.ui.home.aiverse.ui.AiVerseDetailActivity;
import com.offline.bible.ui.home.garden.ui.FaithGardenPlantListActivity;
import com.offline.bible.ui.more.ThemeSettingActivity;
import com.offline.bible.ui.plan.v2.PlanReadActivity;
import com.offline.bible.ui.plan14.Plan14EncourageActivity;
import com.offline.bible.ui.quiz2.QuizHomeActivity;
import com.offline.bible.ui.read.BibleReadDialog;
import com.offline.bible.ui.read.ReadFragment$h;
import com.offline.bible.ui.read.explore.ExploreCDialog;
import com.offline.bible.ui.read.note.BibleNoteListActivity;
import com.offline.bible.utils.SPUtil;
import com.offline.bible.utils.Utils;
import com.offline.bible.views.businessview.homev5.HomeTaskQuizLayoutBigLetter;
import com.offline.bible.views.businessview.homev5.IHomeTaskQuiz$OnQuizFinishClick;
import java.util.ArrayList;
import ua.y3;
import ub.j1;

/* compiled from: R8$$SyntheticClass */
/* loaded from: classes4.dex */
public final /* synthetic */ class e implements View$OnClickListener {
    public final /* synthetic */ int a;
    public final /* synthetic */ Object b;

    public /* synthetic */ e(Object obj, int i) {
        this.a = i;
        this.b = obj;
    }

    @Override // android.view.View$OnClickListener
    public final void onClick(View view) {
        PartForDayPlan partForDayPlan;
        Object obj = this.b;
        switch (this.a) {
            case 0:
                int i = AiVerseDetailActivity.u;
                AiVerseDetailActivity aiVerseDetailActivity = (AiVerseDetailActivity) obj;
                m9.c.c().e(aiVerseDetailActivity.z(aiVerseDetailActivity.r), "ai_verse_share_click");
                j1 j1Var = new j1(aiVerseDetailActivity.o, (Activity) aiVerseDetailActivity);
                ShareContentBean shareContentBean = new ShareContentBean();
                shareContentBean.m(Utils.screenShot(((ua.c) aiVerseDetailActivity.l).b));
                j1Var.e = shareContentBean;
                j1Var.f(true);
                j1Var.show();
                return;
            case 1:
                ((ExploreCDialog) obj).dismiss();
                return;
            case 2:
                int i2 = BibleNoteListActivity.p;
                BibleNoteListActivity bibleNoteListActivity = (BibleNoteListActivity) obj;
                Intent intent = new Intent(bibleNoteListActivity, (Class<?>) MainActivity.class);
                intent.setFlags(AccessibilityEventCompat.TYPE_VIEW_TARGETED_BY_SCROLL);
                intent.putExtra("tagFlag", 1);
                bibleNoteListActivity.startActivity(intent);
                bibleNoteListActivity.finish();
                bibleNoteListActivity.overridePendingTransition(0, 0);
                return;
            case 3:
                int i3 = FaithGardenPlantListActivity.o;
                ((FaithGardenPlantListActivity) obj).finish();
                return;
            case 4:
                HomeTaskQuizLayoutBigLetter.lambda$updateMoreQuizLayout$6((IHomeTaskQuiz$OnQuizFinishClick) obj, view);
                return;
            case 5:
                ((hc.f) obj).dismiss();
                return;
            case 6:
                int i4 = ThemeSettingActivity.l;
                SPUtil.getInstant().save("night_mode_auto_by_system", 1);
                int i5 = App.f.getResources().getConfiguration().uiMode & 48;
                ThemeSettingActivity themeSettingActivity = (ThemeSettingActivity) obj;
                if (i5 == 0 || i5 == 16) {
                    Config config = themeSettingActivity.k;
                    if (config == null) {
                        kotlin.jvm.internal.q.o("mConfig");
                        throw null;
                    }
                    config.g(1);
                } else if (i5 == 32) {
                    Config config2 = themeSettingActivity.k;
                    if (config2 == null) {
                        kotlin.jvm.internal.q.o("mConfig");
                        throw null;
                    }
                    config2.g(2);
                }
                SPUtil instant = SPUtil.getInstant();
                Config config3 = themeSettingActivity.k;
                if (config3 == null) {
                    kotlin.jvm.internal.q.o("mConfig");
                    throw null;
                }
                instant.save("read_config", x.i.e(config3));
                themeSettingActivity.o();
                themeSettingActivity.n();
                m9.c.c().g("More_theme", "3");
                return;
            case 7:
                PlanReadActivity planReadActivity = (PlanReadActivity) obj;
                int i6 = planReadActivity.l;
                if (i6 > 0) {
                    planReadActivity.l = i6 - 1;
                    planReadActivity.p();
                    planReadActivity.o();
                    planReadActivity.n();
                    y3 y3Var = planReadActivity.o;
                    if (y3Var == null) {
                        kotlin.jvm.internal.q.o("mDataBindingLayout");
                        throw null;
                    }
                    y3Var.i.scrollToPosition(0);
                    ArrayList<PartForDayPlan> arrayList = planReadActivity.k;
                    if (arrayList == null || (partForDayPlan = arrayList.get(0)) == null) {
                        return;
                    }
                    m9.c.c().g("NewPlan_Read_Back", String.valueOf(partForDayPlan.getPlan_id()));
                    return;
                }
                return;
            case 8:
                int i7 = Plan14EncourageActivity.l;
                ((Plan14EncourageActivity) obj).getOnBackPressedDispatcher().onBackPressed();
                return;
            case 9:
                CommunityInfoMoreDialog communityInfoMoreDialog = (CommunityInfoMoreDialog) obj;
                communityInfoMoreDialog.dismiss();
                sb.j jVar = communityInfoMoreDialog.b;
                if (jVar != null) {
                    jVar.a.j.g.performClick();
                    return;
                }
                return;
            case 10:
                ((BibleVoiceSelectHKTWDialog) obj).dismiss();
                return;
            case 11:
                NHMSortBySelectDialog nHMSortBySelectDialog = (NHMSortBySelectDialog) obj;
                nHMSortBySelectDialog.b = 1;
                nHMSortBySelectDialog.h();
                nHMSortBySelectDialog.dismiss();
                if ("note".equals(nHMSortBySelectDialog.c)) {
                    m9.c.c().f("notes_edited");
                    return;
                } else if ("highlight".equals(nHMSortBySelectDialog.c)) {
                    m9.c.c().f("highlights_created");
                    return;
                } else {
                    if ("bookmark".equals(nHMSortBySelectDialog.c)) {
                        m9.c.c().f("bookmarks_created");
                        return;
                    }
                    return;
                }
            case 12:
                ((ub.x0) obj).dismiss();
                return;
            case 13:
                UnlockDiscoverQuizDialog unlockDiscoverQuizDialog = (UnlockDiscoverQuizDialog) obj;
                unlockDiscoverQuizDialog.b.getClass();
                if (gb.a.d.get("ca-app-pub-5844091167132219/1663416908") != null) {
                    unlockDiscoverQuizDialog.e = unlockDiscoverQuizDialog.b.b(unlockDiscoverQuizDialog);
                    return;
                }
                unlockDiscoverQuizDialog.e = false;
                unlockDiscoverQuizDialog.c.show();
                unlockDiscoverQuizDialog.b.a();
                return;
            case 14:
                int i8 = QuizHomeActivity.w;
                ((CommonTitleMessageDialog) obj).dismiss();
                return;
            case 15:
                int i9 = HelpCenterDetailActivity.p;
                SPUtil instant2 = SPUtil.getInstant();
                StringBuilder sb = new StringBuilder("help_yes_no_");
                HelpCenterDetailActivity helpCenterDetailActivity = (HelpCenterDetailActivity) obj;
                HelpCenterItem helpCenterItem = helpCenterDetailActivity.o;
                kotlin.jvm.internal.q.d(helpCenterItem);
                sb.append(helpCenterItem.getId());
                instant2.save(sb.toString(), 2);
                helpCenterDetailActivity.z();
                m9.c cVarC = m9.c.c();
                Bundle bundle = new Bundle();
                HelpCenterItem helpCenterItem2 = helpCenterDetailActivity.o;
                kotlin.jvm.internal.q.d(helpCenterItem2);
                bundle.putString("ID", String.valueOf(helpCenterItem2.getId()));
                bundle.putString("comment", "1");
                fh.g0 g0Var = fh.g0.a;
                cVarC.e(bundle, "help_center_faq_comment");
                return;
            case 16:
                ((BibleReadDialog) obj).dismiss();
                return;
            default:
                ((ReadFragment$h) obj).b.d.l.setVisibility(8);
                return;
        }
    }
}
