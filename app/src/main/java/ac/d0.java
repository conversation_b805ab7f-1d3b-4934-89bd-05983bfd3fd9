package ac;

import android.view.View;
import com.offline.bible.ui.home.aiverse.ui.AiVerseJarMainActivity;

/* compiled from: R8$$SyntheticClass */
/* loaded from: classes4.dex */
public final /* synthetic */ class d0 implements th.l {
    public final /* synthetic */ int a;

    public /* synthetic */ d0(int i) {
        this.a = i;
    }

    @Override // th.l
    public final Object invoke(Object obj) {
        switch (this.a) {
            case 0:
                View it = (View) obj;
                int i = AiVerseJarMainActivity.n;
                kotlin.jvm.internal.q.g(it, "it");
                break;
            case 1:
                break;
            case 2:
                break;
            case 3:
                kotlin.jvm.internal.q.g((Throwable) obj, "it");
                break;
            default:
                ((Boolean) obj).booleanValue();
                break;
        }
        return fh.g0.a;
    }
}
