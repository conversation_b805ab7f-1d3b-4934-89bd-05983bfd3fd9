package ac;

import androidx.room.RoomDatabase;
import androidx.sqlite.db.SupportSQLiteDatabase;
import com.offline.bible.ui.home.aiverse.ui.AiVerseDetailActivity;
import com.offline.bible.ui.home.garden.ui.FaithGardenHomeActivity;

/* compiled from: R8$$SyntheticClass */
/* loaded from: classes4.dex */
public final /* synthetic */ class c implements th.l {
    public final /* synthetic */ int a;
    public final /* synthetic */ Object b;

    public /* synthetic */ c(Object obj, int i) {
        this.a = i;
        this.b = obj;
    }

    @Override // th.l
    public final Object invoke(Object obj) {
        Object obj2 = this.b;
        switch (this.a) {
            case 0:
                int i = AiVerseDetailActivity.u;
                ((AiVerseDetailActivity) obj2).s = ((Boolean) obj).booleanValue();
                return fh.g0.a;
            case 1:
                return RoomDatabase.beginTransaction$lambda$8((RoomDatabase) obj2, (SupportSQLiteDatabase) obj);
            default:
                int iIntValue = ((Integer) obj).intValue();
                int i2 = FaithGardenHomeActivity.r;
                ic.o oVarX = ((FaithGardenHomeActivity) obj2).x();
                oVarX.getClass();
                m9.c.c().f("fp_garden_buttonclick_water");
                ta.j.c(oVarX, new ic.n(oVarX, iIntValue, null));
                return fh.g0.a;
        }
    }
}
