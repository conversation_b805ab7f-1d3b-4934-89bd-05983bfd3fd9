package ac;

import com.offline.bible.dao.aiverse.VerseJarModel;
import com.offline.bible.ui.home.aiverse.ui.VerseOfYouActivity;
import java.util.List;

/* compiled from: R8$$SyntheticClass */
/* loaded from: classes4.dex */
public final /* synthetic */ class c1 implements th.a {
    public final /* synthetic */ VerseOfYouActivity a;
    public final /* synthetic */ int b;
    public final /* synthetic */ int c;

    public /* synthetic */ c1(VerseOfYouActivity verseOfYouActivity, int i, int i2) {
        this.a = verseOfYouActivity;
        this.b = i;
        this.c = i2;
    }

    @Override // th.a
    public final Object invoke() {
        int i = VerseOfYouActivity.u;
        VerseOfYouActivity verseOfYouActivity = this.a;
        List<T> list = verseOfYouActivity.z().a;
        int i2 = this.b;
        ((VerseJarModel) list.get(i2)).setLike(this.c);
        verseOfYouActivity.z().notifyItemChanged(i2, "verse_img");
        return fh.g0.a;
    }
}
