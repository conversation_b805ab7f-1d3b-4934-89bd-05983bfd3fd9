package a0;

import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.os.Handler;

/* compiled from: WebpFrameLoader.java */
/* loaded from: classes3.dex */
public class o$a extends u0.c<Bitmap> {
    public final Handler a;
    public final int b;
    public final long c;
    public Bitmap d;

    public o$a(Handler handler, int i, long j) {
        this.a = handler;
        this.b = i;
        this.c = j;
    }

    @Override // u0.j
    public final void onLoadCleared(Drawable drawable) {
        this.d = null;
    }

    @Override // u0.j
    public final void onResourceReady(Object obj, v0.b bVar) {
        this.d = (Bitmap) obj;
        Handler handler = this.a;
        handler.sendMessageAtTime(handler.obtainMessage(1, this), this.c);
    }
}
