package a0;

import android.graphics.Bitmap;
import android.graphics.Bitmap$Config;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Paint$Style;
import android.graphics.PorterDuff$Mode;
import android.graphics.PorterDuffXfermode;
import android.util.DisplayMetrics;
import android.util.Log;
import android.util.LruCache;
import com.bumptech.glide.integration.webp.WebpFrame;
import com.bumptech.glide.integration.webp.WebpImage;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import z.a$a;

/* compiled from: WebpDecoder.java */
/* loaded from: classes3.dex */
public final class h implements z.a {
    public ByteBuffer a;
    public WebpImage b;
    public final a$a c;
    public final int[] e;
    public final com.bumptech.glide.integration.webp.a[] f;
    public final int g;
    public final int h;
    public final int i;
    public final Paint j;
    public final n k;
    public final h$a m;
    public int d = -1;
    public final Bitmap$Config l = Bitmap$Config.ARGB_8888;

    /* JADX WARN: Type inference failed for: r9v2, types: [a0.h$a] */
    public h(a$a a_a, WebpImage webpImage, ByteBuffer byteBuffer, int i, n nVar) {
        this.c = a_a;
        this.b = webpImage;
        this.e = webpImage.getFrameDurations();
        this.f = new com.bumptech.glide.integration.webp.a[webpImage.getFrameCount()];
        for (int i2 = 0; i2 < this.b.getFrameCount(); i2++) {
            this.f[i2] = this.b.getFrameInfo(i2);
            if (Log.isLoggable("WebpDecoder", 3)) {
                Log.d("WebpDecoder", "mFrameInfos: " + this.f[i2].toString());
            }
        }
        this.k = nVar;
        Paint paint = new Paint();
        this.j = paint;
        paint.setColor(0);
        paint.setStyle(Paint$Style.FILL);
        paint.setXfermode(new PorterDuffXfermode(PorterDuff$Mode.SRC));
        final int frameCount = nVar.a == n$a.c ? webpImage.getFrameCount() : Math.max(5, 0);
        this.m = new LruCache<Integer, Bitmap>(frameCount) { // from class: a0.h$a
            @Override // android.util.LruCache
            public final void entryRemoved(boolean z, Integer num, Bitmap bitmap, Bitmap bitmap2) {
                Bitmap bitmap3 = bitmap;
                if (bitmap3 != null) {
                    ((o0.b) this.a.c).a.b(bitmap3);
                }
            }
        };
        new ArrayList();
        if (i <= 0) {
            throw new IllegalArgumentException(androidx.collection.d.c(i, "Sample size must be >=0, not: "));
        }
        int iHighestOneBit = Integer.highestOneBit(i);
        ByteBuffer byteBufferAsReadOnlyBuffer = byteBuffer.asReadOnlyBuffer();
        this.a = byteBufferAsReadOnlyBuffer;
        byteBufferAsReadOnlyBuffer.position(0);
        this.g = iHighestOneBit;
        this.i = this.b.getWidth() / iHighestOneBit;
        this.h = this.b.getHeight() / iHighestOneBit;
    }

    @Override // z.a
    public final Bitmap a() {
        int i;
        Bitmap bitmap;
        int i2 = this.d;
        int i3 = this.i;
        int i4 = this.h;
        Bitmap$Config bitmap$Config = Bitmap$Config.ARGB_8888;
        o0.b bVar = (o0.b) this.c;
        Bitmap bitmapA = bVar.a.a(i3, i4, bitmap$Config);
        bitmapA.eraseColor(0);
        bitmapA.setDensity(DisplayMetrics.DENSITY_DEVICE_STABLE);
        Canvas canvas = new Canvas(bitmapA);
        canvas.drawColor(0, PorterDuff$Mode.SRC);
        boolean z = this.k.a == n$a.a;
        h$a h_a = this.m;
        if (!z && (bitmap = h_a.get(Integer.valueOf(i2))) != null) {
            if (Log.isLoggable("WebpDecoder", 3)) {
                Log.d("WebpDecoder", "hit frame bitmap from memory cache, frameNumber=" + i2);
            }
            bitmap.setDensity(canvas.getDensity());
            canvas.drawBitmap(bitmap, 0.0f, 0.0f, (Paint) null);
            return bitmapA;
        }
        boolean zF = f(i2);
        com.bumptech.glide.integration.webp.a[] aVarArr = this.f;
        if (zF) {
            i = i2;
        } else {
            i = i2 - 1;
            while (true) {
                if (i < 0) {
                    i = 0;
                    break;
                }
                com.bumptech.glide.integration.webp.a aVar = aVarArr[i];
                if (aVar.h && e(aVar)) {
                    break;
                }
                Bitmap bitmap2 = h_a.get(Integer.valueOf(i));
                if (bitmap2 != null && !bitmap2.isRecycled()) {
                    bitmap2.setDensity(canvas.getDensity());
                    canvas.drawBitmap(bitmap2, 0.0f, 0.0f, (Paint) null);
                    if (aVar.h) {
                        d(canvas, aVar);
                    }
                } else {
                    if (f(i)) {
                        break;
                    }
                    i--;
                }
            }
            i++;
        }
        if (Log.isLoggable("WebpDecoder", 3)) {
            Log.d("WebpDecoder", "frameNumber=" + i2 + ", nextIndex=" + i);
        }
        while (i < i2) {
            com.bumptech.glide.integration.webp.a aVar2 = aVarArr[i];
            if (!aVar2.g) {
                d(canvas, aVar2);
            }
            g(canvas, i);
            boolean zIsLoggable = Log.isLoggable("WebpDecoder", 3);
            boolean z2 = aVar2.h;
            if (zIsLoggable) {
                StringBuilder sbE = a2.d.e(i, "renderFrame, index=", ", blend=");
                sbE.append(aVar2.g);
                sbE.append(", dispose=");
                sbE.append(z2);
                Log.d("WebpDecoder", sbE.toString());
            }
            if (z2) {
                d(canvas, aVar2);
            }
            i++;
        }
        com.bumptech.glide.integration.webp.a aVar3 = aVarArr[i2];
        if (!aVar3.g) {
            d(canvas, aVar3);
        }
        g(canvas, i2);
        if (Log.isLoggable("WebpDecoder", 3)) {
            StringBuilder sbE2 = a2.d.e(i2, "renderFrame, index=", ", blend=");
            sbE2.append(aVar3.g);
            sbE2.append(", dispose=");
            sbE2.append(aVar3.h);
            Log.d("WebpDecoder", sbE2.toString());
        }
        h_a.remove(Integer.valueOf(i2));
        Bitmap bitmapA2 = bVar.a.a(bitmapA.getWidth(), bitmapA.getHeight(), bitmapA.getConfig());
        bitmapA2.eraseColor(0);
        bitmapA2.setDensity(bitmapA.getDensity());
        Canvas canvas2 = new Canvas(bitmapA2);
        canvas2.drawColor(0, PorterDuff$Mode.SRC);
        canvas2.drawBitmap(bitmapA, 0.0f, 0.0f, (Paint) null);
        h_a.put(Integer.valueOf(i2), bitmapA2);
        return bitmapA;
    }

    public final void b() {
        this.d = (this.d + 1) % this.b.getFrameCount();
    }

    public final void c() {
        this.b.dispose();
        this.b = null;
        evictAll();
        this.a = null;
    }

    public final void d(Canvas canvas, com.bumptech.glide.integration.webp.a aVar) {
        int i = this.g;
        int i2 = aVar.b;
        int i3 = aVar.c;
        canvas.drawRect(i2 / i, i3 / i, (i2 + aVar.d) / i, (i3 + aVar.e) / i, this.j);
    }

    public final boolean e(com.bumptech.glide.integration.webp.a aVar) {
        if (aVar.b == 0 && aVar.c == 0) {
            if (aVar.d == this.b.getWidth()) {
                if (aVar.e == this.b.getHeight()) {
                    return true;
                }
            }
        }
        return false;
    }

    public final boolean f(int i) {
        if (i == 0) {
            return true;
        }
        com.bumptech.glide.integration.webp.a[] aVarArr = this.f;
        com.bumptech.glide.integration.webp.a aVar = aVarArr[i];
        com.bumptech.glide.integration.webp.a aVar2 = aVarArr[i - 1];
        if (aVar.g || !e(aVar)) {
            return aVar2.h && e(aVar2);
        }
        return true;
    }

    public final void g(Canvas canvas, int i) {
        a$a a_a = this.c;
        com.bumptech.glide.integration.webp.a aVar = this.f[i];
        int i2 = aVar.d;
        int i3 = this.g;
        int i4 = i2 / i3;
        int i5 = aVar.e / i3;
        int i6 = aVar.b / i3;
        int i7 = aVar.c / i3;
        if (i4 == 0 || i5 == 0) {
            return;
        }
        WebpFrame frame = this.b.getFrame(i);
        try {
            try {
                Bitmap bitmapA = ((o0.b) a_a).a.a(i4, i5, this.l);
                bitmapA.eraseColor(0);
                bitmapA.setDensity(canvas.getDensity());
                frame.renderFrame(i4, i5, bitmapA);
                canvas.drawBitmap(bitmapA, i6, i7, (Paint) null);
                ((o0.b) a_a).a.b(bitmapA);
            } catch (IllegalArgumentException | IllegalStateException unused) {
                Log.e("WebpDecoder", "Rendering of frame failed. Frame number: " + i);
            }
        } finally {
            frame.dispose();
        }
    }
}
