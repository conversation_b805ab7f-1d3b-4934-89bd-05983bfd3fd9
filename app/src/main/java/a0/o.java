package a0;

import android.graphics.Bitmap;
import android.os.Handler;
import android.os.Handler$Callback;
import android.os.Looper;
import android.os.Message;
import android.os.SystemClock;
import java.nio.ByteBuffer;
import java.security.MessageDigest;
import java.util.ArrayList;

/* compiled from: WebpFrameLoader.java */
/* loaded from: classes3.dex */
public final class o {
    public static final b0.h<n> r = b0.h.a(n.c, "com.bumptech.glide.integration.webp.decoder.WebpFrameLoader.CacheStrategy");
    public final h a;
    public final Handler b;
    public final ArrayList c;
    public final com.bumptech.glide.l d;
    public final e0.b e;
    public boolean f;
    public boolean g;
    public com.bumptech.glide.k<Bitmap> h;
    public o$a i;
    public boolean j;
    public o$a k;
    public Bitmap l;
    public b0.m<Bitmap> m;
    public o$a n;
    public int o;
    public int p;
    public int q;

    public o(com.bumptech.glide.b bVar, h hVar, int i, int i2, j0.b bVar2, Bitmap bitmap) {
        e0.b bVar3 = bVar.a;
        com.bumptech.glide.g gVar = bVar.c;
        com.bumptech.glide.l lVarD = com.bumptech.glide.b.d(gVar.getBaseContext());
        com.bumptech.glide.l lVarD2 = com.bumptech.glide.b.d(gVar.getBaseContext());
        lVarD2.getClass();
        com.bumptech.glide.k<Bitmap> kVarA = new com.bumptech.glide.k(lVarD2.a, lVarD2, Bitmap.class, lVarD2.b).a(com.bumptech.glide.l.k).a(((t0.g) new t0.g().f(d0.l.a).y()).u(true).m(i, i2));
        this.c = new ArrayList();
        this.f = false;
        this.g = false;
        this.d = lVarD;
        Handler handler = new Handler(Looper.getMainLooper(), new Handler$Callback() { // from class: a0.o$c
            @Override // android.os.Handler$Callback
            public final boolean handleMessage(Message message) {
                int i3 = message.what;
                o oVar = this.a;
                if (i3 == 1) {
                    oVar.b((o$a) message.obj);
                    return true;
                }
                if (i3 != 2) {
                    return false;
                }
                oVar.d.a((o$a) message.obj);
                return false;
            }
        });
        this.e = bVar3;
        this.b = handler;
        this.h = kVarA;
        this.a = hVar;
        c(bVar2, bitmap);
    }

    public final void a() {
        int i;
        if (!this.f || this.g) {
            return;
        }
        o$a o_a = this.n;
        if (o_a != null) {
            this.n = null;
            b(o_a);
            return;
        }
        this.g = true;
        h hVar = this.a;
        int[] iArr = hVar.e;
        long jUptimeMillis = SystemClock.uptimeMillis() + ((iArr.length == 0 || (i = hVar.d) < 0) ? 0 : (i < 0 || i >= iArr.length) ? -1 : iArr[i]);
        hVar.b();
        final int i2 = hVar.d;
        this.k = new o$a(this.b, i2, jUptimeMillis);
        n nVar = hVar.k;
        final w0.d dVar = new w0.d(hVar);
        com.bumptech.glide.k<Bitmap> kVarH = this.h.a(new t0.g().t(new b0.f(dVar, i2) { // from class: a0.o$d
            public final w0.d b;
            public final int c;

            {
                this.b = dVar;
                this.c = i2;
            }

            @Override // b0.f
            public final void b(MessageDigest messageDigest) {
                messageDigest.update(ByteBuffer.allocate(12).putInt(this.c).array());
                this.b.b(messageDigest);
            }

            @Override // b0.f
            public final boolean equals(Object obj) {
                if (!(obj instanceof o$d)) {
                    return false;
                }
                o$d o_d = (o$d) obj;
                return this.b.equals(o_d.b) && this.c == o_d.c;
            }

            @Override // b0.f
            public final int hashCode() {
                return (this.b.b.hashCode() * 31) + this.c;
            }
        }).u(nVar.a == n$a.a)).H(hVar);
        kVarH.F(this.k, kVarH);
    }

    public final void b(o$a o_a) {
        this.g = false;
        boolean z = this.j;
        Handler handler = this.b;
        if (z) {
            handler.obtainMessage(2, o_a).sendToTarget();
            return;
        }
        if (!this.f) {
            this.n = o_a;
            return;
        }
        if (o_a.d != null) {
            Bitmap bitmap = this.l;
            if (bitmap != null) {
                this.e.b(bitmap);
                this.l = null;
            }
            o$a o_a2 = this.i;
            this.i = o_a;
            ArrayList arrayList = this.c;
            for (int size = arrayList.size() - 1; size >= 0; size--) {
                ((o$b) arrayList.get(size)).a();
            }
            if (o_a2 != null) {
                handler.obtainMessage(2, o_a2).sendToTarget();
            }
        }
        a();
    }

    public final void c(b0.m<Bitmap> mVar, Bitmap bitmap) {
        x0.k.c(mVar, "Argument must not be null");
        this.m = mVar;
        x0.k.c(bitmap, "Argument must not be null");
        this.l = bitmap;
        this.h = this.h.a(new t0.g().v(mVar, true));
        this.o = x0.l.c(bitmap);
        this.p = bitmap.getWidth();
        this.q = bitmap.getHeight();
    }
}
