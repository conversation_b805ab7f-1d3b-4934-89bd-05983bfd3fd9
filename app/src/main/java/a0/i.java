package a0;

import android.graphics.Bitmap;
import android.graphics.Bitmap$Config;
import android.graphics.BitmapFactory$Options;
import android.os.Build$VERSION;
import android.os.SystemClock;
import android.util.DisplayMetrics;
import android.util.Log;
import b.u;
import com.bumptech.glide.integration.webp.WebpBitmapFactory;
import com.bumptech.glide.load.ImageHeaderParser$ImageType;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.concurrent.locks.Lock;
import k0.a0;
import k0.m$g;
import mc.FGKS.mMuO;

/* compiled from: WebpDownsampler.java */
/* loaded from: classes3.dex */
public final class i {
    public static final b0.h<Boolean> e = b0.h.a(Boolean.FALSE, "com.bumptech.glide.integration.webp.decoder.WebpDownsampler.DisableDecoder");
    public static final i$a f = new i$a();
    public static final ArrayDeque g;
    public final e0.b a;
    public final DisplayMetrics b;
    public final e0.g c;
    public final ArrayList d;

    static {
        char[] cArr = x0.l.a;
        g = new ArrayDeque(0);
    }

    public i(ArrayList arrayList, DisplayMetrics displayMetrics, e0.b bVar, e0.g gVar) {
        this.d = arrayList;
        x0.k.c(displayMetrics, "Argument must not be null");
        this.b = displayMetrics;
        x0.k.c(bVar, "Argument must not be null");
        this.a = bVar;
        x0.k.c(gVar, "Argument must not be null");
        this.c = gVar;
    }

    /* JADX WARN: Code restructure failed: missing block: B:31:?, code lost:
    
        throw r6;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    public static Bitmap c(InputStream inputStream, BitmapFactory$Options bitmapFactory$Options, i$a i_a, e0.b bVar) throws IOException {
        if (bitmapFactory$Options.inJustDecodeBounds) {
            inputStream.mark(10485760);
        } else {
            i_a.getClass();
        }
        int i = bitmapFactory$Options.outWidth;
        int i2 = bitmapFactory$Options.outHeight;
        String str = bitmapFactory$Options.outMimeType;
        Lock lock = a0.d;
        lock.lock();
        try {
            try {
                Bitmap bitmapDecodeStream = WebpBitmapFactory.decodeStream(inputStream, null, bitmapFactory$Options);
                lock.unlock();
                if (bitmapFactory$Options.inJustDecodeBounds) {
                    inputStream.reset();
                }
                return bitmapDecodeStream;
            } catch (IllegalArgumentException e2) {
                StringBuilder sbE = u.e(i, "Exception decoding bitmap, outWidth: ", i2, ", outHeight: ", ", outMimeType: ");
                sbE.append(str);
                sbE.append(", inBitmap: ");
                sbE.append(d(bitmapFactory$Options.inBitmap));
                IOException iOException = new IOException(sbE.toString(), e2);
                if (Log.isLoggable("WebpDownsampler", 3)) {
                    Log.d("WebpDownsampler", "Failed to decode with inBitmap, trying again without Bitmap re-use", iOException);
                }
                if (bitmapFactory$Options.inBitmap == null) {
                    throw iOException;
                }
                try {
                    inputStream.reset();
                    bVar.b(bitmapFactory$Options.inBitmap);
                    bitmapFactory$Options.inBitmap = null;
                    Bitmap bitmapC = c(inputStream, bitmapFactory$Options, i_a, bVar);
                    a0.d.unlock();
                    return bitmapC;
                } catch (IOException unused) {
                    throw iOException;
                }
            }
        } catch (Throwable th) {
            a0.d.unlock();
            throw th;
        }
    }

    public static String d(Bitmap bitmap) {
        if (bitmap == null) {
            return null;
        }
        return "[" + bitmap.getWidth() + "x" + bitmap.getHeight() + "] " + bitmap.getConfig() + (" (" + bitmap.getAllocationByteCount() + ")");
    }

    public static void e(BitmapFactory$Options bitmapFactory$Options) {
        bitmapFactory$Options.inTempStorage = null;
        bitmapFactory$Options.inDither = false;
        bitmapFactory$Options.inScaled = false;
        bitmapFactory$Options.inSampleSize = 1;
        bitmapFactory$Options.inPreferredConfig = null;
        bitmapFactory$Options.inJustDecodeBounds = false;
        bitmapFactory$Options.inDensity = 0;
        bitmapFactory$Options.inTargetDensity = 0;
        bitmapFactory$Options.outWidth = 0;
        bitmapFactory$Options.outHeight = 0;
        bitmapFactory$Options.outMimeType = null;
        bitmapFactory$Options.inBitmap = null;
        bitmapFactory$Options.inMutable = true;
    }

    public final k0.e a(InputStream inputStream, int i, int i2, b0.i iVar) {
        ArrayDeque arrayDeque;
        BitmapFactory$Options bitmapFactory$Options;
        BitmapFactory$Options bitmapFactory$Options2;
        i$a i_a = f;
        x0.k.a("You must provide an InputStream that supports mark()", inputStream.markSupported());
        byte[] bArr = (byte[]) this.c.d(byte[].class, 65536);
        synchronized (i.class) {
            arrayDeque = g;
            synchronized (arrayDeque) {
                bitmapFactory$Options = (BitmapFactory$Options) arrayDeque.poll();
            }
            if (bitmapFactory$Options == null) {
                bitmapFactory$Options = new BitmapFactory$Options();
                e(bitmapFactory$Options);
            }
            bitmapFactory$Options2 = bitmapFactory$Options;
        }
        bitmapFactory$Options2.inTempStorage = bArr;
        b0.b bVar = (b0.b) iVar.c(k0.n.f);
        k0.m mVar = (k0.m) iVar.c(k0.n.h);
        boolean zBooleanValue = ((Boolean) iVar.c(k0.n.i)).booleanValue();
        b0.h<Boolean> hVar = k0.n.j;
        if (iVar.c(hVar) != null) {
            ((Boolean) iVar.c(hVar)).booleanValue();
        }
        try {
            k0.e eVarB = k0.e.b(b(inputStream, bitmapFactory$Options2, mVar, bVar, i, i2, zBooleanValue, i_a), this.a);
            e(bitmapFactory$Options2);
            synchronized (arrayDeque) {
                arrayDeque.offer(bitmapFactory$Options2);
            }
            this.c.h(bArr);
            return eVarB;
        } catch (Throwable th) {
            e(bitmapFactory$Options2);
            ArrayDeque arrayDeque2 = g;
            synchronized (arrayDeque2) {
                arrayDeque2.offer(bitmapFactory$Options2);
                this.c.h(bArr);
                throw th;
            }
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:67:0x0191 A[ADDED_TO_REGION] */
    /* JADX WARN: Removed duplicated region for block: B:70:0x019b  */
    /* JADX WARN: Removed duplicated region for block: B:73:0x01a9  */
    /* JADX WARN: Removed duplicated region for block: B:74:0x01ee  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    public final Bitmap b(InputStream inputStream, BitmapFactory$Options bitmapFactory$Options, k0.m mVar, b0.b bVar, int i, int i2, boolean z, i$a i_a) throws IOException {
        char c;
        String str;
        e0.b bVar2;
        String str2;
        boolean z2;
        String str3;
        int i3;
        String str4;
        boolean z3;
        e0.b bVar3;
        int iRound;
        int i4;
        e0.b bVar4;
        boolean zHasAlpha;
        int iFloor;
        int iFloor2;
        k0.m mVar2;
        int i5;
        int iCeil;
        int iCeil2;
        int i6;
        InputStream inputStream2 = inputStream;
        int i7 = x0.g.b;
        long jElapsedRealtimeNanos = SystemClock.elapsedRealtimeNanos();
        bitmapFactory$Options.inJustDecodeBounds = true;
        e0.b bVar5 = this.a;
        c(inputStream2, bitmapFactory$Options, i_a, bVar5);
        bitmapFactory$Options.inJustDecodeBounds = false;
        int[] iArr = {bitmapFactory$Options.outWidth, bitmapFactory$Options.outHeight};
        int i8 = iArr[0];
        int i9 = iArr[1];
        String str5 = bitmapFactory$Options.outMimeType;
        ArrayList arrayList = this.d;
        e0.g gVar = this.c;
        int iA = com.bumptech.glide.load.a.a(arrayList, inputStream2, gVar);
        switch (iA) {
            case 3:
            case 4:
                c = 180;
                break;
            case 5:
            case 6:
                c = 'Z';
                break;
            case 7:
            case 8:
                c = 270;
                break;
            default:
                c = 0;
                break;
        }
        int i10 = i == Integer.MIN_VALUE ? i8 : i;
        int iRound2 = i2 == Integer.MIN_VALUE ? i9 : i2;
        ImageHeaderParser$ImageType imageHeaderParser$ImageTypeB = com.bumptech.glide.load.a.b(arrayList, inputStream2, gVar);
        if (i8 <= 0 || i9 <= 0) {
            str = "WebpDownsampler";
            bVar2 = bVar5;
            str2 = ", density: ";
            z2 = false;
            str3 = "x";
            i3 = i10;
            str4 = ", target density: ";
        } else {
            float fB = (c == 'Z' || c == 270) ? mVar.b(i9, i8, i10, iRound2) : mVar.b(i8, i9, i10, iRound2);
            if (fB <= 0.0f) {
                int i11 = i10;
                StringBuilder sb = new StringBuilder("Cannot scale with factor: ");
                sb.append(fB);
                sb.append(" from: ");
                sb.append(mVar);
                sb.append(", source: [");
                androidx.collection.a.j(sb, i8, "x", i9, "], target: [");
                sb.append(i11);
                sb.append("x");
                sb.append(iRound2);
                sb.append("]");
                throw new IllegalArgumentException(sb.toString());
            }
            m$g m_gA = mVar.a(i8, i9, i10, iRound2);
            if (m_gA == null) {
                throw new IllegalArgumentException("Cannot round with null rounding");
            }
            float f2 = i8;
            float f3 = i9;
            int i12 = i10;
            int i13 = i8 / ((int) ((fB * f2) + 0.5d));
            int i14 = i9 / ((int) ((fB * f3) + 0.5d));
            m$g m_g = m$g.a;
            int iMax = Math.max(1, Integer.highestOneBit(m_gA == m_g ? Math.max(i13, i14) : Math.min(i13, i14)));
            if (m_gA == m_g && iMax < 1.0f / fB) {
                iMax <<= 1;
            }
            bitmapFactory$Options.inSampleSize = iMax;
            if (imageHeaderParser$ImageTypeB == ImageHeaderParser$ImageType.JPEG) {
                float fMin = Math.min(iMax, 8);
                iCeil = (int) Math.ceil(f2 / fMin);
                iCeil2 = (int) Math.ceil(f3 / fMin);
                int i15 = iMax / 8;
                if (i15 > 0) {
                    iCeil /= i15;
                    iCeil2 /= i15;
                }
            } else {
                if (imageHeaderParser$ImageTypeB == ImageHeaderParser$ImageType.PNG || imageHeaderParser$ImageTypeB == ImageHeaderParser$ImageType.PNG_A) {
                    inputStream2 = inputStream;
                    float f4 = iMax;
                    iFloor = (int) Math.floor(f2 / f4);
                    iFloor2 = (int) Math.floor(f3 / f4);
                } else if (imageHeaderParser$ImageTypeB == ImageHeaderParser$ImageType.WEBP || imageHeaderParser$ImageTypeB == ImageHeaderParser$ImageType.WEBP_A) {
                    inputStream2 = inputStream;
                    float f5 = iMax;
                    iFloor = Math.round(f2 / f5);
                    iFloor2 = Math.round(f3 / f5);
                } else if (i8 % iMax == 0 && i9 % iMax == 0) {
                    iCeil = i8 / iMax;
                    iCeil2 = i9 / iMax;
                } else {
                    bitmapFactory$Options.inJustDecodeBounds = true;
                    inputStream2 = inputStream;
                    c(inputStream2, bitmapFactory$Options, i_a, bVar5);
                    bitmapFactory$Options.inJustDecodeBounds = false;
                    int[] iArr2 = {bitmapFactory$Options.outWidth, bitmapFactory$Options.outHeight};
                    int i16 = iArr2[0];
                    i5 = iArr2[1];
                    mVar2 = mVar;
                    iCeil = i16;
                    i3 = i12;
                    double dB = mVar2.b(iCeil, i5, i3, iRound2);
                    bVar2 = bVar5;
                    i6 = (int) (((dB / (r11 / 1.0E9f)) * ((int) ((1.0E9d * dB) + 0.5d))) + 0.5d);
                    bitmapFactory$Options.inTargetDensity = i6;
                    bitmapFactory$Options.inDensity = 1000000000;
                    if (i6 <= 0 || i6 == 1000000000) {
                        z2 = false;
                        bitmapFactory$Options.inTargetDensity = 0;
                        bitmapFactory$Options.inDensity = 0;
                        str = "WebpDownsampler";
                    } else {
                        bitmapFactory$Options.inScaled = true;
                        str = "WebpDownsampler";
                        z2 = false;
                    }
                    if (Log.isLoggable(str, 2)) {
                        str2 = ", density: ";
                        str4 = ", target density: ";
                        str3 = "x";
                    } else {
                        str3 = "x";
                        StringBuilder sbE = u.e(i8, "Calculate scaling, source: [", i9, str3, "], target: [");
                        androidx.collection.a.j(sbE, i3, str3, iRound2, "], power of two scaled: [");
                        androidx.collection.a.j(sbE, iCeil, str3, i5, "], exact scale factor: ");
                        sbE.append(fB);
                        sbE.append(", power of 2 sample size: ");
                        sbE.append(iMax);
                        sbE.append(", adjusted scale factor: ");
                        sbE.append(dB);
                        str4 = ", target density: ";
                        sbE.append(str4);
                        sbE.append(bitmapFactory$Options.inTargetDensity);
                        str2 = ", density: ";
                        sbE.append(str2);
                        sbE.append(bitmapFactory$Options.inDensity);
                        Log.v(str, sbE.toString());
                    }
                }
                mVar2 = mVar;
                i3 = i12;
                int i17 = iFloor;
                i5 = iFloor2;
                iCeil = i17;
                double dB2 = mVar2.b(iCeil, i5, i3, iRound2);
                bVar2 = bVar5;
                i6 = (int) (((dB2 / (r11 / 1.0E9f)) * ((int) ((1.0E9d * dB2) + 0.5d))) + 0.5d);
                bitmapFactory$Options.inTargetDensity = i6;
                bitmapFactory$Options.inDensity = 1000000000;
                if (i6 <= 0) {
                    z2 = false;
                    bitmapFactory$Options.inTargetDensity = 0;
                    bitmapFactory$Options.inDensity = 0;
                    str = "WebpDownsampler";
                    if (Log.isLoggable(str, 2)) {
                    }
                }
            }
            mVar2 = mVar;
            i5 = iCeil2;
            i3 = i12;
            inputStream2 = inputStream;
            double dB22 = mVar2.b(iCeil, i5, i3, iRound2);
            bVar2 = bVar5;
            i6 = (int) (((dB22 / (r11 / 1.0E9f)) * ((int) ((1.0E9d * dB22) + 0.5d))) + 0.5d);
            bitmapFactory$Options.inTargetDensity = i6;
            bitmapFactory$Options.inDensity = 1000000000;
            if (i6 <= 0) {
            }
        }
        if (bVar != b0.b.a) {
            try {
                zHasAlpha = com.bumptech.glide.load.a.b(arrayList, inputStream2, gVar).hasAlpha();
            } catch (IOException e2) {
                if (Log.isLoggable(str, 3)) {
                    Log.d(str, "Cannot determine whether the image has alpha or not from header, format " + bVar, e2);
                }
                zHasAlpha = z2;
            }
            Bitmap$Config bitmap$Config = zHasAlpha ? Bitmap$Config.ARGB_8888 : Bitmap$Config.RGB_565;
            bitmapFactory$Options.inPreferredConfig = bitmap$Config;
            if (bitmap$Config == Bitmap$Config.RGB_565 || bitmap$Config == Bitmap$Config.ARGB_4444 || bitmap$Config == Bitmap$Config.ALPHA_8) {
                z3 = true;
                bitmapFactory$Options.inDither = true;
            } else {
                z3 = true;
            }
        } else {
            z3 = true;
            bitmapFactory$Options.inPreferredConfig = Bitmap$Config.ARGB_8888;
        }
        int i18 = Build$VERSION.SDK_INT;
        if (z) {
            iRound = i3;
            bVar3 = bVar2;
        } else {
            int i19 = bitmapFactory$Options.inTargetDensity;
            if (i19 <= 0 || (i4 = bitmapFactory$Options.inDensity) <= 0 || i19 == i4) {
                z3 = z2;
            }
            float f6 = z3 ? i19 / bitmapFactory$Options.inDensity : 1.0f;
            int i20 = bitmapFactory$Options.inSampleSize;
            float f7 = i20;
            bVar3 = bVar2;
            int iCeil3 = (int) Math.ceil(i8 / f7);
            int iCeil4 = (int) Math.ceil(i9 / f7);
            iRound = Math.round(iCeil3 * f6);
            iRound2 = Math.round(iCeil4 * f6);
            if (Log.isLoggable(str, 2)) {
                StringBuilder sbE2 = u.e(iRound, "Calculated target [", iRound2, str3, "] for source [");
                androidx.collection.a.j(sbE2, i8, str3, i9, mMuO.AIpwlGjyldqOe);
                sbE2.append(i20);
                sbE2.append(", targetDensity: ");
                sbE2.append(bitmapFactory$Options.inTargetDensity);
                sbE2.append(str2);
                sbE2.append(bitmapFactory$Options.inDensity);
                sbE2.append(", density multiplier: ");
                sbE2.append(f6);
                Log.v(str, sbE2.toString());
            }
        }
        if (iRound <= 0 || iRound2 <= 0 || (i18 >= 26 && bitmapFactory$Options.inPreferredConfig == Bitmap$Config.HARDWARE)) {
            bVar4 = bVar3;
        } else {
            bVar4 = bVar3;
            bitmapFactory$Options.inBitmap = bVar4.a(iRound, iRound2, bitmapFactory$Options.inPreferredConfig);
        }
        Bitmap bitmapC = c(inputStream2, bitmapFactory$Options, i_a, bVar4);
        i_a.getClass();
        if (Log.isLoggable(str, 2)) {
            Log.v(str, "Decoded " + d(bitmapC) + " from [" + i8 + str3 + i9 + "] " + str5 + " with inBitmap " + d(bitmapFactory$Options.inBitmap) + " for [" + i + str3 + i2 + "], sample size: " + bitmapFactory$Options.inSampleSize + str2 + bitmapFactory$Options.inDensity + str4 + bitmapFactory$Options.inTargetDensity + ", thread: " + Thread.currentThread().getName() + ", duration: " + x0.g.a(jElapsedRealtimeNanos));
        }
        if (bitmapC == null) {
            return null;
        }
        bitmapC.setDensity(this.b.densityDpi);
        Bitmap bitmapC2 = a0.c(bVar4, bitmapC, iA);
        if (bitmapC.equals(bitmapC2)) {
            return bitmapC2;
        }
        bVar4.b(bitmapC);
        return bitmapC2;
    }
}
