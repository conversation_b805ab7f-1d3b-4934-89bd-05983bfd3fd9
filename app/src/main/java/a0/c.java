package a0;

import android.graphics.Bitmap;
import d0.v;
import java.nio.ByteBuffer;
import java.util.concurrent.atomic.AtomicReference;
import x0.a$a;

/* compiled from: ByteBufferBitmapWebpDecoder.java */
/* loaded from: classes3.dex */
public final class c implements b0.k<ByteBuffer, Bitmap> {
    public final i a;

    public c(i iVar) {
        this.a = iVar;
    }

    @Override // b0.k
    public final v<Bitmap> a(ByteBuffer byteBuffer, int i, int i2, b0.i iVar) {
        AtomicReference<byte[]> atomicReference = x0.a.a;
        return this.a.a(new a$a(byteBuffer), i, i2, iVar);
    }

    @Override // b0.k
    public final boolean b(ByteBuffer byteBuffer, b0.i iVar) {
        this.a.getClass();
        ((Boolean) iVar.c(i.e)).booleanValue();
        return false;
    }
}
