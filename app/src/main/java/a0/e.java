package a0;

import android.graphics.Bitmap;
import com.bumptech.glide.integration.webp.b$e;
import d0.v;
import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;

/* compiled from: StreamAnimatedBitmapDecoder.java */
/* loaded from: classes3.dex */
public final class e implements b0.k<InputStream, Bitmap> {
    public final a a;

    public e(a aVar) {
        this.a = aVar;
    }

    @Override // b0.k
    public final v<Bitmap> a(InputStream inputStream, int i, int i2, b0.i iVar) throws IOException {
        a aVar = this.a;
        byte[] bArrH = p7.b.h(inputStream);
        if (bArrH == null) {
            return null;
        }
        return aVar.a(ByteBuffer.wrap(bArrH), i, i2);
    }

    @Override // b0.k
    public final boolean b(InputStream inputStream, b0.i iVar) {
        return !((<PERSON><PERSON><PERSON>) iVar.c(a.d)).booleanValue() && com.bumptech.glide.integration.webp.b.b(inputStream, this.a.a) == b$e.f;
    }
}
