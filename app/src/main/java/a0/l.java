package a0;

import android.graphics.Bitmap;

/* compiled from: WebpDrawableResource.java */
/* loaded from: classes3.dex */
public final class l extends m0.c<j> {
    @Override // d0.v
    public final Class<j> a() {
        return j.class;
    }

    @Override // d0.v
    public final int getSize() {
        o oVar = ((j) this.a).a.a;
        return oVar.a.b.getSizeInBytes() + oVar.o;
    }

    @Override // m0.c, d0.r
    public final void initialize() {
        ((j) this.a).a.a.l.prepareToDraw();
    }

    @Override // d0.v
    public final void recycle() {
        j jVar = (j) this.a;
        jVar.stop();
        jVar.d = true;
        o oVar = jVar.a.a;
        oVar.c.clear();
        Bitmap bitmap = oVar.l;
        if (bitmap != null) {
            oVar.e.b(bitmap);
            oVar.l = null;
        }
        oVar.f = false;
        o$a o_a = oVar.i;
        com.bumptech.glide.l lVar = oVar.d;
        if (o_a != null) {
            lVar.a(o_a);
            oVar.i = null;
        }
        o$a o_a2 = oVar.k;
        if (o_a2 != null) {
            lVar.a(o_a2);
            oVar.k = null;
        }
        o$a o_a3 = oVar.n;
        if (o_a3 != null) {
            lVar.a(o_a3);
            oVar.n = null;
        }
        oVar.a.c();
        oVar.j = true;
    }
}
