package a0;

import com.bumptech.glide.integration.webp.WebpImage;
import java.nio.ByteBuffer;

/* compiled from: AnimatedWebpBitmapDecoder.java */
/* loaded from: classes3.dex */
public final class a {
    public static final b0.h<Boolean> d = b0.h.a(Boolean.FALSE, "com.bumptech.glide.integration.webp.decoder.AnimatedWebpBitmapDecoder.DisableBitmap");
    public final e0.g a;
    public final e0.b b;
    public final o0.b c;

    public a(e0.b bVar, e0.g gVar) {
        this.a = gVar;
        this.b = bVar;
        this.c = new o0.b(bVar, gVar);
    }

    public final k0.e a(ByteBuffer byteBuffer, int i, int i2) {
        int iRemaining = byteBuffer.remaining();
        byte[] bArr = new byte[iRemaining];
        byteBuffer.get(bArr, 0, iRemaining);
        WebpImage webpImageCreate = WebpImage.create(bArr);
        h hVar = new h(this.c, webpImageCreate, byteBuffer, p7.b.g(webpImageCreate.getWidth(), webpImageCreate.getHeight(), i, i2), n.b);
        try {
            hVar.b();
            return k0.e.b(hVar.a(), this.b);
        } finally {
            hVar.c();
        }
    }
}
