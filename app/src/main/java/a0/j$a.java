package a0;

import android.content.res.Resources;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.Drawable$ConstantState;

/* compiled from: WebpDrawable.java */
/* loaded from: classes3.dex */
public class j$a extends Drawable$ConstantState {
    public final o a;

    public j$a(e0.b bVar, o oVar) {
        this.a = oVar;
    }

    @Override // android.graphics.drawable.Drawable$ConstantState
    public final int getChangingConfigurations() {
        return 0;
    }

    @Override // android.graphics.drawable.Drawable$ConstantState
    public final Drawable newDrawable() {
        return new j(this);
    }

    @Override // android.graphics.drawable.Drawable$ConstantState
    public final Drawable newDrawable(Resources resources) {
        return new j(this);
    }
}
