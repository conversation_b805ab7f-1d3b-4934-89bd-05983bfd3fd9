package a0;

import android.content.Context;
import android.graphics.Bitmap;
import d0.v;
import java.security.MessageDigest;
import java.util.Objects;

/* compiled from: WebpDrawableTransformation.java */
/* loaded from: classes3.dex */
public final class m implements b0.m<j> {
    public final k0.j b;

    public m(k0.j jVar) {
        this.b = jVar;
    }

    @Override // b0.m
    public final v<j> a(Context context, v<j> vVar, int i, int i2) {
        j jVar = vVar.get();
        v<Bitmap> eVar = new k0.e(jVar.a.a.l, com.bumptech.glide.b.b(context).a);
        k0.j jVar2 = this.b;
        v<Bitmap> vVarA = jVar2.a(context, eVar, i, i2);
        if (!eVar.equals(vVarA)) {
            eVar.recycle();
        }
        jVar.a.a.c(jVar2, vVarA.get());
        return vVar;
    }

    @Override // b0.f
    public final void b(MessageDigest messageDigest) {
        this.b.b(messageDigest);
    }

    @Override // b0.f
    public final boolean equals(Object obj) {
        if (!(obj instanceof m)) {
            return false;
        }
        k0.j jVar = ((m) obj).b;
        this.b.getClass();
        return Objects.nonNull(jVar);
    }

    @Override // b0.f
    public final int hashCode() {
        this.b.getClass();
        return -670243078;
    }
}
