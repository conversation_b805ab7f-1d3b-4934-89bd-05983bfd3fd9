package a0;

import com.bumptech.glide.integration.webp.b$e;
import d0.v;
import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;

/* compiled from: StreamWebpDecoder.java */
/* loaded from: classes3.dex */
public final class g implements b0.k<InputStream, j> {
    public static final b0.h<Boolean> c = b0.h.a(Boolean.FALSE, "com.bumptech.glide.integration.webp.decoder.StreamWebpDecoder.DisableAnimation");
    public final d a;
    public final e0.g b;

    public g(d dVar, e0.g gVar) {
        this.a = dVar;
        this.b = gVar;
    }

    @Override // b0.k
    public final v<j> a(InputStream inputStream, int i, int i2, b0.i iVar) throws IOException {
        byte[] bArrH = p7.b.h(inputStream);
        if (bArrH == null) {
            return null;
        }
        return this.a.a(ByteBuffer.wrap(bArrH), i, i2, iVar);
    }

    @Override // b0.k
    public final boolean b(InputStream inputStream, b0.i iVar) {
        return !((Boolean) iVar.c(c)).booleanValue() && com.bumptech.glide.integration.webp.b.b(inputStream, this.b) == b$e.f;
    }
}
