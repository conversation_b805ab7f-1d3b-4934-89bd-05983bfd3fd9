package a0;

import android.graphics.Bitmap;
import com.bumptech.glide.integration.webp.b$b;
import com.bumptech.glide.integration.webp.b$e;
import d0.v;
import java.nio.ByteBuffer;

/* compiled from: ByteBufferAnimatedBitmapDecoder.java */
/* loaded from: classes3.dex */
public final class b implements b0.k<ByteBuffer, Bitmap> {
    public final a a;

    public b(a aVar) {
        this.a = aVar;
    }

    @Override // b0.k
    public final v<Bitmap> a(ByteBuffer byteBuffer, int i, int i2, b0.i iVar) {
        return this.a.a(byteBuffer, i, i2);
    }

    @Override // b0.k
    public final boolean b(ByteBuffer byteBuffer, b0.i iVar) {
        ByteBuffer byteBuffer2 = byteBuffer;
        if (((Boolean) iVar.c(a.d)).booleanValue()) {
            return false;
        }
        return (byteBuffer2 == null ? b$e.g : com.bumptech.glide.integration.webp.b.a(new b$b(byteBuffer2))) == b$e.f;
    }
}
