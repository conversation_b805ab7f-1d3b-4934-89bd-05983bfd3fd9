package a0;

import android.content.Context;
import android.graphics.Bitmap;
import com.bumptech.glide.integration.webp.WebpImage;
import com.bumptech.glide.integration.webp.b$b;
import com.bumptech.glide.integration.webp.b$e;
import d0.v;
import java.nio.ByteBuffer;

/* compiled from: ByteBufferWebpDecoder.java */
/* loaded from: classes3.dex */
public final class d implements b0.k<ByteBuffer, j> {
    public static final b0.h<Boolean> d = b0.h.a(Boolean.FALSE, "com.bumptech.glide.integration.webp.decoder.ByteBufferWebpDecoder.DisableAnimation");
    public final Context a;
    public final e0.b b;
    public final o0.b c;

    public d(Context context, e0.g gVar, e0.b bVar) {
        this.a = context.getApplicationContext();
        this.b = bVar;
        this.c = new o0.b(bVar, gVar);
    }

    @Override // b0.k
    public final v<j> a(ByteBuffer byteBuffer, int i, int i2, b0.i iVar) {
        ByteBuffer byteBuffer2 = byteBuffer;
        int iRemaining = byteBuffer2.remaining();
        byte[] bArr = new byte[iRemaining];
        byteBuffer2.get(bArr, 0, iRemaining);
        WebpImage webpImageCreate = WebpImage.create(bArr);
        h hVar = new h(this.c, webpImageCreate, byteBuffer2, p7.b.g(webpImageCreate.getWidth(), webpImageCreate.getHeight(), i, i2), (n) iVar.c(o.r));
        hVar.b();
        Bitmap bitmapA = hVar.a();
        return new l(new j(new j$a(this.b, new o(com.bumptech.glide.b.b(this.a), hVar, i, i2, j0.b.b, bitmapA))));
    }

    @Override // b0.k
    public final boolean b(ByteBuffer byteBuffer, b0.i iVar) {
        ByteBuffer byteBuffer2 = byteBuffer;
        if (((Boolean) iVar.c(d)).booleanValue()) {
            return false;
        }
        return (byteBuffer2 == null ? b$e.g : com.bumptech.glide.integration.webp.b.a(new b$b(byteBuffer2))) == b$e.f;
    }
}
