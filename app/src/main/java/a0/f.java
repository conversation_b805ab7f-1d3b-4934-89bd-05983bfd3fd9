package a0;

import android.graphics.Bitmap;
import d0.v;
import java.io.InputStream;

/* compiled from: StreamBitmapWebpDecoder.java */
/* loaded from: classes3.dex */
public final class f implements b0.k<InputStream, Bitmap> {
    public final i a;

    public f(i iVar, e0.g gVar) {
        this.a = iVar;
    }

    @Override // b0.k
    public final v<Bitmap> a(InputStream inputStream, int i, int i2, b0.i iVar) {
        return this.a.a(inputStream, i, i2, iVar);
    }

    @Override // b0.k
    public final boolean b(InputStream inputStream, b0.i iVar) {
        this.a.getClass();
        ((Boolean) iVar.c(i.e)).booleanValue();
        return false;
    }
}
