package a0;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* compiled from: WebpFrameCacheStrategy.java */
/* loaded from: classes3.dex */
public final class n$a {
    public static final n$a a;
    public static final n$a b;
    public static final n$a c;
    public static final /* synthetic */ n$a[] d;

    static {
        n$a n_a = new n$a("CACHE_NONE", 0);
        a = n_a;
        n$a n_a2 = new n$a("CACHE_LIMITED", 1);
        n$a n_a3 = new n$a("CACHE_AUTO", 2);
        b = n_a3;
        n$a n_a4 = new n$a("CACHE_ALL", 3);
        c = n_a4;
        d = new n$a[]{n_a, n_a2, n_a3, n_a4};
    }

    public n$a() {
        throw null;
    }

    public static n$a valueOf(String str) {
        return (n$a) Enum.valueOf(n$a.class, str);
    }

    public static n$a[] values() {
        return (n$a[]) d.clone();
    }
}
