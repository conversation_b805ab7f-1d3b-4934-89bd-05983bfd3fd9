package a0;

import android.os.Bundle;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;
import androidx.compose.ui.jO.TpeYhFK;
import b.v1;
import com.adjust.sdk.Adjust;
import com.adjust.sdk.AdjustAdRevenue;
import com.adjust.sdk.AdjustEvent;
import com.google.android.gms.measurement.api.AppMeasurementSdk$ConditionalUserProperty;
import com.offline.bible.App;
import com.offline.bible.utils.SPUtil;
import com.offline.bible.utils.TimeUtils;
import d0.v;
import dl.b2;
import dl.f1;
import dl.u0;
import e1.p;
import g5.b0;
import h5.r;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.regex.Pattern;
import ki.f0;
import kotlin.jvm.internal.q;
import rf.zo.JUhagZqR;
import ya.e1;

/* compiled from: WebpDrawableEncoder.java */
/* loaded from: classes3.dex */
public final class k implements b0.l, f0.a, h5.j {
    public static ArrayList a;

    public static final u0 g(zk.c cVar) {
        return new u0(b2.a, cVar);
    }

    public static final byte h(char c) {
        if (c < '~') {
            return fl.e.b[c];
        }
        return (byte) 0;
    }

    public static void i(String str, String str2) {
        if (SPUtil.getInstant().getBoolean(str, false)) {
            return;
        }
        m9.c.c().f(str);
        Adjust.trackEvent(new AdjustEvent(str2));
        SPUtil.getInstant().save(str, Boolean.TRUE);
    }

    public static final zk.c k(zk.c cVar) {
        q.g(cVar, "<this>");
        return cVar.getDescriptor().b() ? cVar : new f1(cVar);
    }

    public static final boolean l(String str) {
        if (str == null || str.length() == 0 || str.length() < 43 || str.length() > 128) {
            return false;
        }
        Pattern patternCompile = Pattern.compile("^[-._~A-Za-z0-9]+$");
        q.f(patternCompile, "compile(...)");
        return patternCompile.matcher(str).matches();
    }

    public static final void n(si.a aVar, si.b from, ki.e scopeOwner, jj.f name) {
        q.g(aVar, "<this>");
        q.g(from, "from");
        q.g(scopeOwner, "scopeOwner");
        q.g(name, "name");
    }

    public static final void o(si.a aVar, si.b from, f0 scopeOwner, jj.f name) {
        q.g(aVar, "<this>");
        q.g(from, "from");
        q.g(scopeOwner, "scopeOwner");
        q.g(name, "name");
        scopeOwner.c().b();
        q.f(name.c(), "name.asString()");
    }

    public static float p(float f) {
        return (((f * 2.0f) - 2.0f) * (-1.0f)) - 1.0f;
    }

    public static void q(ClassLoader classLoader, HashSet hashSet, h5.m mVar) {
        if (hashSet.isEmpty()) {
            return;
        }
        HashSet hashSet2 = new HashSet();
        Iterator it = hashSet.iterator();
        while (it.hasNext()) {
            hashSet2.add(((File) it.next()).getParentFile());
        }
        Field fieldF = v1.f(classLoader, "pathList");
        try {
            Object objCast = Object.class.cast(fieldF.get(classLoader));
            h5.q qVar = new h5.q(objCast, v1.f(objCast, "nativeLibraryDirectories"), List.class);
            synchronized (b0.class) {
                ArrayList arrayList = new ArrayList((Collection) qVar.a());
                hashSet2.removeAll(arrayList);
                arrayList.addAll(hashSet2);
                qVar.b(arrayList);
            }
            ArrayList arrayList2 = new ArrayList();
            Object[] objArrA = mVar.a(objCast, new ArrayList(hashSet2), arrayList2);
            if (arrayList2.isEmpty()) {
                synchronized (b0.class) {
                    v1.c(objCast, "nativeLibraryPathElements", Object.class).d(Arrays.asList(objArrA));
                }
                return;
            }
            h5.o oVar = new h5.o("Error in makePathElements");
            int size = arrayList2.size();
            for (int i = 0; i < size; i++) {
                try {
                    Throwable.class.getDeclaredMethod("addSuppressed", Throwable.class).invoke(oVar, (IOException) arrayList2.get(i));
                } catch (Exception unused) {
                }
            }
            throw oVar;
        } catch (Exception e) {
            String name = fieldF.getName();
            String name2 = classLoader.getClass().getName();
            String name3 = Object.class.getName();
            StringBuilder sbE = androidx.datastore.preferences.protobuf.a.e("Failed to get value of field ", name, " of type ", name2, " on object of type ");
            sbE.append(name3);
            throw new r(sbE.toString(), e);
        }
    }

    @Override // b0.l
    public b0.c c(b0.i iVar) {
        return b0.c.a;
    }

    @Override // b0.d
    public boolean d(Object obj, File file, b0.i iVar) throws Throwable {
        try {
            x0.a.c(((j) ((v) obj).get()).a.a.a.a.asReadOnlyBuffer(), file);
            return true;
        } catch (IOException e) {
            if (Log.isLoggable("WebpEncoder", 5)) {
                Log.w("WebpEncoder", "Failed to encode WebP drawable data", e);
            }
            return false;
        }
    }

    @Override // f0.a
    public void e(b0.f fVar, d0.g gVar) {
    }

    @Override // f0.a
    public File f(b0.f fVar) {
        return null;
    }

    public static final String j(String codeVerifier) {
        q.g(codeVerifier, "codeVerifier");
        if (!l(codeVerifier)) {
            throw new p(TpeYhFK.PYAaTzZZgt);
        }
        try {
            byte[] bytes = codeVerifier.getBytes(lk.a.c);
            q.f(bytes, "(this as java.lang.String).getBytes(charset)");
            MessageDigest messageDigest = MessageDigest.getInstance("SHA-256");
            messageDigest.update(bytes, 0, bytes.length);
            String strEncodeToString = Base64.encodeToString(messageDigest.digest(), 11);
            q.f(strEncodeToString, "{\n      // try to generate challenge with S256\n      val bytes: ByteArray = codeVerifier.toByteArray(Charsets.US_ASCII)\n      val messageDigest = MessageDigest.getInstance(\"SHA-256\")\n      messageDigest.update(bytes, 0, bytes.size)\n      val digest = messageDigest.digest()\n\n      Base64.encodeToString(digest, Base64.URL_SAFE or Base64.NO_PADDING or Base64.NO_WRAP)\n    }");
            return strEncodeToString;
        } catch (Exception e) {
            throw new p(e);
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:53:0x0202  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    public static void m(f3.i iVar, String str, String str2, String str3) {
        int iC;
        String str4;
        double d;
        double d2 = iVar.b / 1000000.0d;
        String str5 = iVar.a + "";
        Bundle bundle = new Bundle();
        bundle.putDouble(AppMeasurementSdk$ConditionalUserProperty.VALUE, d2);
        bundle.putString("currency", "USD");
        bundle.putString("precisionType", str5);
        if (!TextUtils.isEmpty(str)) {
            bundle.putString("adNetwork", str);
        }
        bundle.putString("format", str2);
        bundle.putString("unionid", str3);
        m9.c.c().e(bundle, "ad_impression_revenue");
        if ("Interstitial".equals(str2)) {
            m9.c.c().e(bundle, "inters_ad_impression");
        }
        App context = App.f;
        q.g(context, "context");
        new com.facebook.appevents.p(context, (String) null).e("AdImpression", d2, androidx.compose.material3.c.b("fb_currency", "USD"));
        if (ya.q.a().a) {
            AdjustAdRevenue adjustAdRevenue = new AdjustAdRevenue("admob_sdk");
            adjustAdRevenue.setRevenue(Double.valueOf(d2), "USD");
            adjustAdRevenue.setAdRevenueNetwork(str);
            adjustAdRevenue.setAdRevenueUnit(str3);
            adjustAdRevenue.setAdRevenuePlacement(str2);
            Adjust.trackAdRevenue(adjustAdRevenue);
        }
        m9.e.d().getClass();
        double dF = m9.e.f(50);
        m9.e.d().getClass();
        double dF2 = m9.e.f(40);
        m9.e.d().getClass();
        double dF3 = m9.e.f(30);
        m9.e.d().getClass();
        double dF4 = m9.e.f(20);
        m9.e.d().getClass();
        double dF5 = m9.e.f(10);
        if (dF > 0.0d && dF2 > 0.0d && dF3 > 0.0d && dF4 > 0.0d && dF5 > 0.0d) {
            SPUtil.getInstant().remove("TaichitCPAOnedayAdRevenueCache-" + TimeUtils.getYestodayDate());
            double dFloatValue = ((Float) SPUtil.getInstant().get("TaichitCPAOnedayAdRevenueCache-" + TimeUtils.getTodayDate(), Float.valueOf(0.0f))).floatValue();
            float f = (float) (dFloatValue + d2);
            SPUtil.getInstant().save("TaichitCPAOnedayAdRevenueCache-" + TimeUtils.getTodayDate(), Float.valueOf(f));
            int i = 0;
            double[] dArr = {dF, dF2, dF3, dF4, dF5};
            for (int i2 = 5; i < i2; i2 = 5) {
                double d3 = dArr[i];
                if (dFloatValue < d3) {
                    d = dFloatValue;
                    if (f >= d3) {
                        Bundle bundle2 = new Bundle();
                        bundle2.putDouble(AppMeasurementSdk$ConditionalUserProperty.VALUE, dArr[i]);
                        bundle2.putString("currency", "USD");
                        String str6 = i != 0 ? i != 1 ? i != 2 ? i != 3 ? "AdLTV_OneDay_Top10Percent" : "AdLTV_OneDay_Top20Percent" : "AdLTV_OneDay_Top30Percent" : "AdLTV_OneDay_Top40Percent" : "AdLTV_OneDay_Top50Percent";
                        m9.c.c().e(bundle2, str6);
                        App context2 = App.f;
                        q.g(context2, "context");
                        com.facebook.appevents.p pVar = new com.facebook.appevents.p(context2, (String) null);
                        Bundle bundle3 = new Bundle();
                        bundle3.putString(AppMeasurementSdk$ConditionalUserProperty.VALUE, dArr[i] + "");
                        bundle3.putString("fb_currency", "USD");
                        pVar.d(bundle3, str6);
                    }
                } else {
                    d = dFloatValue;
                }
                i++;
                dFloatValue = d;
            }
        }
        if (ai.l.g() && (iC = e1.c()) <= 7) {
            float f2 = (float) (SPUtil.getInstant().getFloat("TaichitCPAOnedayAdRevenueCacheD3AndD7", 0.0f) + d2);
            if (iC > 3) {
                str4 = "TaichitCPAOnedayAdRevenueCacheD3AndD7";
            } else {
                m9.e.d().getClass();
                double dB = m9.e.b(50);
                m9.e.d().getClass();
                double dB2 = m9.e.b(40);
                m9.e.d().getClass();
                double dB3 = m9.e.b(30);
                m9.e.d().getClass();
                double dB4 = m9.e.b(20);
                m9.e.d().getClass();
                double dB5 = m9.e.b(10);
                if (dB > 0.0d && dB2 > 0.0d && dB3 > 0.0d && dB4 > 0.0d && dB5 > 0.0d) {
                    str4 = "TaichitCPAOnedayAdRevenueCacheD3AndD7";
                    double d4 = f2;
                    if (d4 >= dB && d4 < dB2) {
                        i("BFW_AND_AdLTV_D3_Top50", "e22sp2");
                    } else if (d4 >= dB2 && d4 < dB3) {
                        i("BFW_AND_AdLTV_D3_Top40", "2oqb3z");
                    } else if (d4 >= dB3 && d4 < dB4) {
                        i("BFW_AND_AdLTV_D3_Top30", "2l9fku");
                    } else if (d4 >= dB4 && d4 < dB5) {
                        i("BFW_AND_AdLTV_D3_Top20", "g8jnkr");
                    } else if (d4 >= dB5) {
                        i("BFW_AND_AdLTV_D3_Top10", "b4we73");
                    }
                }
            }
            if (iC <= 7) {
                m9.e.d().getClass();
                double dC = m9.e.c(50);
                m9.e.d().getClass();
                double dC2 = m9.e.c(40);
                m9.e.d().getClass();
                double dC3 = m9.e.c(30);
                m9.e.d().getClass();
                double dC4 = m9.e.c(20);
                m9.e.d().getClass();
                double dC5 = m9.e.c(10);
                if (dC > 0.0d && dC2 > 0.0d && dC3 > 0.0d && dC4 > 0.0d && dC5 > 0.0d) {
                    double d5 = f2;
                    if (d5 >= dC && d5 < dC2) {
                        i("BFW_AND_AdLTV_D7_Top50", "stxlhi");
                    } else if (d5 >= dC2 && d5 < dC3) {
                        i("BFW_AND_AdLTV_D7_Top40", "gievj8");
                    } else if (d5 >= dC3 && d5 < dC4) {
                        i("BFW_AND_AdLTV_D7_Top30", "4zcgvc");
                    } else if (d5 >= dC4 && d5 < dC5) {
                        i("BFW_AND_AdLTV_D7_Top20", "dk4s38");
                    } else if (d5 >= dC5) {
                        i("BFW_AND_AdLTV_D7_Top10", JUhagZqR.xgpqHyjpStglxN);
                    }
                }
            }
            SPUtil.getInstant().save(str4, Float.valueOf(f2));
        }
    }
}
