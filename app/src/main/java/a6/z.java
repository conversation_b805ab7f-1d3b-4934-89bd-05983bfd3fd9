package a6;

import android.os.Looper;
import com.google.android.gms.internal.firebase-auth-api.zzg;
import java.util.concurrent.Executor;

/* compiled from: com.google.firebase:firebase-auth@@21.0.6 */
/* loaded from: classes3.dex */
public final class z implements Executor {
    public static final z b = new z();
    public final zzg a = new zzg(Looper.getMainLooper());

    @Override // java.util.concurrent.Executor
    public final void execute(Runnable runnable) {
        this.a.post(runnable);
    }
}
