package a4;

import android.content.Context;
import android.graphics.Color;
import android.util.TypedValue;
import android.view.View;
import androidx.core.graphics.ColorUtils;
import l4.b;

/* compiled from: MaterialColors.java */
/* loaded from: classes3.dex */
public final class a {
    public static int a(int i, int i2) {
        return ColorUtils.setAlphaComponent(i, (Color.alpha(i) * i2) / 255);
    }

    public static int b(Context context, int i, int i2) {
        TypedValue typedValueA = b.a(i, context);
        return typedValueA != null ? typedValueA.data : i2;
    }

    public static int c(View view, int i) {
        return b.b(view.getContext(), i, view.getClass().getCanonicalName());
    }

    public static boolean d(int i) {
        return i != 0 && ColorUtils.calculateLuminance(i) > 0.5d;
    }

    public static int e(float f, int i, int i2) {
        return ColorUtils.compositeColors(ColorUtils.setAlphaComponent(i2, Math.round(Color.alpha(i2) * f)), i);
    }
}
