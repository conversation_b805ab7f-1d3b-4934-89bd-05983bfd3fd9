package a1;

import android.util.SparseIntArray;
import e0.i;
import java.util.ArrayDeque;
import java.util.List;
import x0.l;

/* compiled from: BaseMultiTypeDelegate.kt */
/* loaded from: classes3.dex */
public abstract class a {
    public final Cloneable a;

    public a() {
        char[] cArr = l.a;
        this.a = new ArrayDeque(20);
    }

    public abstract int a(int i, List list);

    public void b(i iVar) {
        ArrayDeque arrayDeque = (ArrayDeque) this.a;
        if (arrayDeque.size() < 20) {
            arrayDeque.offer(iVar);
        }
    }

    public a(int i) {
        this.a = new SparseIntArray();
    }
}
