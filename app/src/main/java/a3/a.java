package a3;

import android.util.SparseArray;
import java.util.HashMap;
import n2.d;

/* compiled from: PriorityMapping.java */
/* loaded from: classes3.dex */
public final class a {
    public static final SparseArray<d> a = new SparseArray<>();
    public static final HashMap<d, Integer> b;

    static {
        HashMap<d, Integer> map = new HashMap<>();
        b = map;
        map.put(d.a, 0);
        map.put(d.b, 1);
        map.put(d.c, 2);
        for (d dVar : map.keySet()) {
            a.append(b.get(dVar).intValue(), dVar);
        }
    }

    public static int a(d dVar) {
        Integer num = b.get(dVar);
        if (num != null) {
            return num.intValue();
        }
        throw new IllegalStateException("PriorityMapping is missing known Priority value " + dVar);
    }

    public static d b(int i) {
        d dVar = a.get(i);
        if (dVar != null) {
            return dVar;
        }
        throw new IllegalArgumentException(androidx.collection.d.c(i, "Unknown Priority for value "));
    }
}
