package a2;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import com.bible.holy.bible.for.women.R$id;
import com.bible.holy.bible.for.women.R$layout;
import kotlin.jvm.internal.q;

/* compiled from: ToolTipPopup.kt */
/* loaded from: classes3.dex */
public final class l$a extends FrameLayout {
    public final ImageView a;
    public final ImageView b;
    public final View c;
    public final ImageView d;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public l$a(l this$0, Context context) {
        super(context);
        q.g(this$0, "this$0");
        q.g(context, "context");
        LayoutInflater.from(context).inflate(R$layout.ec, this);
        View viewFindViewById = findViewById(R$id.lw);
        if (viewFindViewById == null) {
            throw new NullPointerException("null cannot be cast to non-null type android.widget.ImageView");
        }
        this.a = (ImageView) viewFindViewById;
        View viewFindViewById2 = findViewById(R$id.lu);
        if (viewFindViewById2 == null) {
            throw new NullPointerException("null cannot be cast to non-null type android.widget.ImageView");
        }
        this.b = (ImageView) viewFindViewById2;
        View viewFindViewById3 = findViewById(R$id.ln);
        q.f(viewFindViewById3, "findViewById(R.id.com_facebook_body_frame)");
        this.c = viewFindViewById3;
        View viewFindViewById4 = findViewById(R$id.lo);
        if (viewFindViewById4 == null) {
            throw new NullPointerException("null cannot be cast to non-null type android.widget.ImageView");
        }
        this.d = (ImageView) viewFindViewById4;
    }
}
