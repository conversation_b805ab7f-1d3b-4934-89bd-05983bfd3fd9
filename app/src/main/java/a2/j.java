package a2;

import android.content.Intent;
import android.content.SharedPreferences$Editor;
import android.view.animation.TranslateAnimation;
import androidx.core.view.animation.PathInterpolatorCompat;
import androidx.core.widget.ContentLoadingProgressBar;
import androidx.recyclerview.widget.LinearLayoutManager;
import bb.a$a;
import bb.a$d;
import com.facebook.internal.h0;
import com.facebook.internal.h0$a;
import com.facebook.internal.v;
import com.facebook.internal.v$a;
import com.facebook.internal.v$e;
import com.google.firebase.perf.metrics.AppStartTrace;
import com.offline.bible.entity.pray.GospelPsalmsModel;
import com.offline.bible.ui.dialog.HowAreYouContentV2Dialog;
import com.offline.bible.ui.dialog.TopicContentDialog;
import com.offline.bible.ui.home.v7.HomeFragmentV7;
import com.offline.bible.ui.quiz.QuizEncourageActivity;
import com.offline.bible.ui.quiz3.activity.QuizPuzzleMainActivity;
import com.offline.bible.ui.read.ReadFragment;
import com.offline.bible.ui.splash.LaunchActivity;
import com.offline.bible.utils.MetricsUtils;
import e1.f0;
import fh.g0;
import j7.x;
import java.io.File;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.PriorityQueue;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.ReentrantLock;
import kotlin.jvm.internal.q;
import ua.q5;
import ub.q1;
import ub.w0;
import x7.m;
import x7.m$b;

/* compiled from: R8$$SyntheticClass */
/* loaded from: classes3.dex */
public final /* synthetic */ class j implements Runnable {
    public final /* synthetic */ int a;
    public final /* synthetic */ Object b;

    public /* synthetic */ j(Object obj, int i) {
        this.a = i;
        this.b = obj;
    }

    @Override // java.lang.Runnable
    public final void run() {
        long j;
        int i = 1;
        int i2 = 0;
        switch (this.a) {
            case 0:
                l this$0 = (l) this.b;
                if (w1.a.b(l.class)) {
                    return;
                }
                try {
                    q.g(this$0, "this$0");
                    this$0.a();
                    return;
                } catch (Throwable th) {
                    w1.a.a(th, l.class);
                    return;
                }
            case 1:
                ((th.a) this.b).invoke();
                return;
            case 2:
                ((ContentLoadingProgressBar) this.b).showOnUiThread();
                return;
            case 3:
                a$d a_d = ((a$a) this.b).a.c;
                if (a_d != null) {
                    a_d.b();
                    return;
                }
                return;
            case 4:
                v this$02 = (v) this.b;
                q.g(this$02, "this$0");
                Condition condition = this$02.e;
                ReentrantLock reentrantLock = this$02.d;
                reentrantLock.lock();
                try {
                    this$02.c = false;
                    g0 g0Var = g0.a;
                    reentrantLock.unlock();
                    try {
                        h0$a h0_a = h0.c;
                        f0 f0Var = f0.c;
                        h0$a.a(f0Var, "v", "trim started");
                        PriorityQueue priorityQueue = new PriorityQueue();
                        File[] fileArrListFiles = this$02.b.listFiles(v$a.a);
                        long length = 0;
                        if (fileArrListFiles != null) {
                            int length2 = fileArrListFiles.length;
                            j = 0;
                            while (i2 < length2) {
                                File file = fileArrListFiles[i2];
                                i2 += i;
                                q.f(file, "file");
                                v$e v_e = new v$e(file);
                                priorityQueue.add(v_e);
                                h0$a h0_a2 = h0.c;
                                StringBuilder sb = new StringBuilder();
                                File[] fileArr = fileArrListFiles;
                                sb.append("  trim considering time=");
                                sb.append(Long.valueOf(v_e.b));
                                sb.append(" name=");
                                sb.append((Object) file.getName());
                                h0$a.a(f0Var, "v", sb.toString());
                                length += file.length();
                                j++;
                                fileArrListFiles = fileArr;
                                i = 1;
                            }
                        } else {
                            j = 0;
                        }
                        while (true) {
                            if (length <= 1048576 && j <= 1024) {
                                reentrantLock.lock();
                                try {
                                    condition.signalAll();
                                    g0 g0Var2 = g0.a;
                                    return;
                                } finally {
                                }
                            }
                            File file2 = ((v$e) priorityQueue.remove()).a;
                            h0$a h0_a3 = h0.c;
                            h0$a.a(f0Var, "v", q.m(file2.getName(), "  trim removing "));
                            length -= file2.length();
                            j--;
                            file2.delete();
                        }
                    } catch (Throwable th2) {
                        reentrantLock.lock();
                        try {
                            condition.signalAll();
                            g0 g0Var3 = g0.a;
                            throw th2;
                        } finally {
                        }
                    }
                } finally {
                }
                break;
            case 5:
                ((w0) this.b).getClass();
                return;
            case 6:
                com.vungle.ads.internal.executor.f.submit$lambda-2((com.vungle.ads.internal.executor.f) this.b);
                return;
            case 7:
                x xVar = (x) this.b;
                synchronized (xVar.d) {
                    SharedPreferences$Editor sharedPreferences$EditorEdit = xVar.a.edit();
                    String str = xVar.b;
                    StringBuilder sb2 = new StringBuilder();
                    Iterator<String> it = xVar.d.iterator();
                    while (it.hasNext()) {
                        sb2.append(it.next());
                        sb2.append(xVar.c);
                    }
                    sharedPreferences$EditorEdit.putString(str, sb2.toString()).commit();
                }
                return;
            case 8:
                String str2 = HomeFragmentV7.p;
                HomeFragmentV7 homeFragmentV7 = (HomeFragmentV7) this.b;
                if (homeFragmentV7.getActivity() == null || homeFragmentV7.isStateSaved()) {
                    return;
                }
                homeFragmentV7.o = true;
                homeFragmentV7.u(false);
                return;
            case 9:
                LaunchActivity launchActivity = (LaunchActivity) this.b;
                if (LaunchActivity.M) {
                    return;
                }
                if (!launchActivity.isFinishing() && !launchActivity.isDestroyed() && launchActivity.g) {
                    launchActivity.finish();
                    return;
                }
                if (launchActivity.u && !LaunchActivity.M) {
                    LaunchActivity.M = eb.a.a().h(3, false);
                    if (LaunchActivity.M) {
                        return;
                    }
                }
                launchActivity.q();
                return;
            case 10:
                AppStartTrace appStartTrace = AppStartTrace.p;
                AppStartTrace appStartTrace2 = (AppStartTrace) this.b;
                m$b m_bA = m.A();
                m_bA.s("_as");
                m_bA.q(appStartTrace2.g.a);
                m_bA.r(appStartTrace2.g.b(appStartTrace2.j));
                ArrayList arrayList = new ArrayList(3);
                m$b m_bA2 = m.A();
                m_bA2.s("_astui");
                m_bA2.q(appStartTrace2.g.a);
                m_bA2.r(appStartTrace2.g.b(appStartTrace2.h));
                arrayList.add(m_bA2.build());
                m$b m_bA3 = m.A();
                m_bA3.s("_astfd");
                m_bA3.q(appStartTrace2.h.a);
                m_bA3.r(appStartTrace2.h.b(appStartTrace2.i));
                arrayList.add(m_bA3.build());
                m$b m_bA4 = m.A();
                m_bA4.s("_asti");
                m_bA4.q(appStartTrace2.i.a);
                m_bA4.r(appStartTrace2.i.b(appStartTrace2.j));
                arrayList.add(m_bA4.build());
                m_bA.k(arrayList);
                m_bA.l(appStartTrace2.m.a());
                appStartTrace2.b.c(m_bA.build(), x7.d.e);
                return;
            case 11:
                int i3 = QuizEncourageActivity.u;
                QuizEncourageActivity quizEncourageActivity = (QuizEncourageActivity) this.b;
                if (quizEncourageActivity.g) {
                    return;
                }
                quizEncourageActivity.d.dismiss();
                if (quizEncourageActivity.w()) {
                    return;
                }
                Intent intent = new Intent();
                intent.putExtra("resurgence", true);
                quizEncourageActivity.setResult(-1, intent);
                quizEncourageActivity.finish();
                return;
            case 12:
                ((HowAreYouContentV2Dialog) this.b).dismiss();
                return;
            case 13:
                TopicContentDialog topicContentDialog = (TopicContentDialog) this.b;
                if (topicContentDialog.getActivity() == null) {
                    return;
                }
                bb.a aVar = topicContentDialog.s;
                if (aVar != null) {
                    topicContentDialog.q = false;
                    aVar.c = new q1(topicContentDialog);
                    topicContentDialog.s.b(true);
                }
                ub.h0 h0Var = topicContentDialog.d;
                if (h0Var == null || !h0Var.isShowing()) {
                    return;
                }
                topicContentDialog.d.dismiss();
                return;
            case 14:
                int i4 = QuizPuzzleMainActivity.y;
                ((q5) ((QuizPuzzleMainActivity) this.b).l).w.setVisibility(8);
                return;
            case 15:
                ReadFragment readFragment = (ReadFragment) this.b;
                LinearLayoutManager linearLayoutManager = (LinearLayoutManager) readFragment.e.getLayoutManager();
                if (linearLayoutManager != null) {
                    linearLayoutManager.scrollToPositionWithOffset(readFragment.O.getItemCount() - 1, 0);
                }
                readFragment.A();
                readFragment.d.j.setVisibility(0);
                if (readFragment.getActivity() == null) {
                    return;
                }
                TranslateAnimation translateAnimation = new TranslateAnimation(0.0f, 0.0f, 0.0f, -MetricsUtils.dp2px(readFragment.getActivity(), 12.0f));
                translateAnimation.setDuration(800L);
                translateAnimation.setRepeatMode(2);
                translateAnimation.setRepeatCount(6);
                translateAnimation.setInterpolator(PathInterpolatorCompat.create(0.42f, 0.0f, 0.58f, 1.0f));
                readFragment.d.j.startAnimation(translateAnimation);
                return;
            default:
                yd.a aVar2 = (yd.a) this.b;
                GospelPsalmsModel gospelPsalmsModelA = yd.a.a();
                if (gospelPsalmsModelA != null) {
                    aVar2.d.postValue(gospelPsalmsModelA);
                    return;
                }
                return;
        }
    }
}
