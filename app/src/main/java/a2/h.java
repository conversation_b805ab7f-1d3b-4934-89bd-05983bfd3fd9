package a2;

import ads_mobile_sdk.sj1;
import android.view.View;
import android.view.View$OnClickListener;
import android.view.ViewConfiguration;
import android.widget.TextView;
import androidx.compose.ui.graphics.colorspace.DoubleFunction;
import androidx.compose.ui.graphics.colorspace.Rgb$Companion;
import androidx.compose.ui.graphics.colorspace.TransferParameters;
import androidx.core.util.Supplier;
import androidx.media3.common.Player$Listener;
import androidx.media3.common.SimpleBasePlayer;
import androidx.media3.common.SimpleBasePlayer$State;
import androidx.media3.common.util.ListenerSet$Event;
import androidx.media3.exoplayer.analytics.AnalyticsListener;
import androidx.media3.exoplayer.analytics.AnalyticsListener$EventTime;
import com.bible.holy.bible.for.women.R$color;
import com.bible.holy.bible.for.women.R$id;
import com.bible.holy.bible.for.women.R$string;
import com.facebook.internal.b0$a;
import com.offline.bible.dao.note.BookMark;
import com.offline.bible.dao.note.BookNoteDbManager;
import com.offline.bible.ui.dialog.CommonTitleMessageDialog;
import com.offline.bible.ui.home.advent.PrayCandleActivityForAdvent;
import com.offline.bible.ui.read.bookmarks.BibleBookMarksListActivity;
import com.offline.bible.utils.ColorUtils;
import fb.f$b;
import java.util.List;
import kotlin.jvm.internal.q;
import ua.o4;
import ub.b1;
import wg.a$a;

/* compiled from: R8$$SyntheticClass */
/* loaded from: classes3.dex */
public final /* synthetic */ class h implements b0$a, DoubleFunction, Supplier, ListenerSet$Event, mg.g, f$b, d1.a {
    public final /* synthetic */ int a;
    public final /* synthetic */ Object b;

    public /* synthetic */ h(Object obj, int i) {
        this.a = i;
        this.b = obj;
    }

    @Override // fb.f$b
    public void b(sj1 sj1Var) {
        int i = PrayCandleActivityForAdvent.r;
        m9.c.c().f("Ad_DXD_Native_show");
        PrayCandleActivityForAdvent prayCandleActivityForAdvent = (PrayCandleActivityForAdvent) this.b;
        o4 o4Var = prayCandleActivityForAdvent.l;
        if (o4Var == null) {
            q.o("viewDataBinding");
            throw null;
        }
        o4Var.b.setVisibility(0);
        o4 o4Var2 = prayCandleActivityForAdvent.l;
        if (o4Var2 == null) {
            q.o("viewDataBinding");
            throw null;
        }
        o4Var2.b.setBackgroundColor(ColorUtils.getColor(R$color.f4));
        fb.f fVarB = fb.f.b();
        o4 o4Var3 = prayCandleActivityForAdvent.l;
        if (o4Var3 == null) {
            q.o("viewDataBinding");
            throw null;
        }
        fVarB.getClass();
        View viewF = fb.f.f(sj1Var, o4Var3.b, 0.0f);
        ((TextView) viewF.findViewById(R$id.c1)).setTextColor(ColorUtils.getColor(R$color.cq));
        ((TextView) viewF.findViewById(R$id.bu)).setTextColor(ColorUtils.getColor(R$color.e7));
    }

    @Override // d1.a
    public void d(z0.f adapter, View view, final int i) {
        int i2 = BibleBookMarksListActivity.p;
        q.g(adapter, "adapter");
        if (view.getId() == 2131362356) {
            final CommonTitleMessageDialog commonTitleMessageDialog = new CommonTitleMessageDialog();
            final BibleBookMarksListActivity bibleBookMarksListActivity = (BibleBookMarksListActivity) this.b;
            commonTitleMessageDialog.m = bibleBookMarksListActivity.getString(R$string.to);
            commonTitleMessageDialog.n = bibleBookMarksListActivity.getString(R$string.tn);
            b1 b1Var = new b1(commonTitleMessageDialog, 1);
            commonTitleMessageDialog.c = R$string.o1;
            commonTitleMessageDialog.j = b1Var;
            commonTitleMessageDialog.f = ColorUtils.getColor(R$color.df);
            View$OnClickListener view$OnClickListener = new View$OnClickListener() { // from class: zc.a
                @Override // android.view.View$OnClickListener
                public final void onClick(View view2) {
                    int i3 = BibleBookMarksListActivity.p;
                    commonTitleMessageDialog.dismiss();
                    BookNoteDbManager bookNoteDbManager = BookNoteDbManager.getInstance();
                    BibleBookMarksListActivity bibleBookMarksListActivity2 = bibleBookMarksListActivity;
                    e eVar = bibleBookMarksListActivity2.m;
                    if (eVar == null) {
                        q.o("mBookMarksListAdapter");
                        throw null;
                    }
                    List<T> list = eVar.a;
                    int i4 = i;
                    bookNoteDbManager.delBookMark((BookMark) list.get(i4));
                    e eVar2 = bibleBookMarksListActivity2.m;
                    if (eVar2 == null) {
                        q.o("mBookMarksListAdapter");
                        throw null;
                    }
                    eVar2.remove(i4);
                    e eVar3 = bibleBookMarksListActivity2.m;
                    if (eVar3 == null) {
                        q.o("mBookMarksListAdapter");
                        throw null;
                    }
                    if (eVar3.getItemCount() == 0) {
                        bibleBookMarksListActivity2.x();
                    }
                }
            };
            commonTitleMessageDialog.b = R$string.tt;
            commonTitleMessageDialog.i = view$OnClickListener;
            commonTitleMessageDialog.h(bibleBookMarksListActivity.getSupportFragmentManager());
        }
    }

    @Override // androidx.core.util.Supplier
    public Object get() {
        return Integer.valueOf(((ViewConfiguration) this.b).getScaledMaximumFlingVelocity());
    }

    @Override // androidx.compose.ui.graphics.colorspace.DoubleFunction
    public double invoke(double d) {
        return Rgb$Companion.generateEotf$lambda$4((TransferParameters) this.b, d);
    }

    @Override // mg.g
    public void subscribe(mg.e eVar) {
        ((BookNoteDbManager) this.b).lambda$getGroupMarkReads$7((a$a) eVar);
    }

    @Override // androidx.media3.common.util.ListenerSet$Event
    public void invoke(Object obj) {
        switch (this.a) {
            case 3:
                SimpleBasePlayer.lambda$updateStateAndInformListeners$40((SimpleBasePlayer$State) this.b, (Player$Listener) obj);
                break;
            default:
                ((AnalyticsListener) obj).onPlayerReleased((AnalyticsListener$EventTime) this.b);
                break;
        }
    }
}
