package a2;

import ads_mobile_sdk.a9;
import ads_mobile_sdk.j72;
import android.view.result.ActivityResultCallback;
import androidx.compose.ui.geometry.Rect;
import androidx.compose.ui.internal.InlineClassHelperKt;
import androidx.compose.ui.text.TextInclusionStrategy;
import androidx.compose.ui.text.TextInclusionStrategy$Companion;
import b.ki;
import b.nk;
import b6.u;
import com.facebook.login.widget.LoginButton;
import com.google.firebase.sessions.FirebaseSessionsRegistrar;

/* compiled from: R8$$SyntheticClass */
/* loaded from: classes3.dex */
public final /* synthetic */ class d implements ActivityResultCallback, TextInclusionStrategy, nk, b6.d {
    public static int a(int i, int i2, boolean z) {
        return (Boolean.hashCode(z) + i) * i2;
    }

    public static fh.h b(String str) {
        InlineClassHelperKt.throwIllegalStateExceptionForNullCheck(str);
        return new fh.h();
    }

    public static String d(String str, String str2, String str3, String str4) {
        return str + str2 + str3 + str4;
    }

    public static StringBuilder e(int i, String str, String str2) {
        StringBuilder sb = new StringBuilder(str);
        sb.append(i);
        sb.append(str2);
        return sb;
    }

    @Override // b.nk
    public ki c(j72 j72Var) {
        return a9.a(j72Var);
    }

    @Override // b6.d
    public Object g(u uVar) {
        return FirebaseSessionsRegistrar.getComponents$lambda-3(uVar);
    }

    @Override // androidx.compose.ui.text.TextInclusionStrategy
    public boolean isIncluded(Rect rect, Rect rect2) {
        return TextInclusionStrategy$Companion.ContainsCenter$lambda$2(rect, rect2);
    }

    @Override // android.view.result.ActivityResultCallback
    public void onActivityResult(Object obj) {
        int i = LoginButton.y;
    }
}
