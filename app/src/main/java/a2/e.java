package a2;

import ads_mobile_sdk.sj1;
import android.graphics.Bitmap;
import androidx.window.embedding.ExtensionEmbeddingBackend$SplitListenerWrapper;
import c6.k$a;
import com.facebook.internal.r;
import com.facebook.internal.x0;
import com.facebook.internal.x0$b;
import com.facebook.login.widget.LoginButton;
import com.offline.bible.dao.dailygospel.GospelCollectManager;
import com.offline.bible.dao.dailygospel.GospelModel;
import com.offline.bible.ui.more.MoreFragment;
import com.offline.bible.utils.TaskService;
import com.offline.bible.utils.Utils;
import fb.f$b;
import java.util.ArrayList;
import java.util.concurrent.Callable;
import java.util.concurrent.atomic.AtomicReference;
import kotlin.jvm.internal.q;
import ya.a1;

/* compiled from: R8$$SyntheticClass */
/* loaded from: classes3.dex */
public final /* synthetic */ class e implements Runnable {
    public final /* synthetic */ int a;
    public final /* synthetic */ Object b;
    public final /* synthetic */ Object c;

    public /* synthetic */ e(int i, Object obj, Object obj2) {
        this.a = i;
        this.b = obj;
        this.c = obj2;
    }

    @Override // java.lang.Runnable
    public final void run() {
        r rVar;
        Object obj = this.c;
        Object obj2 = this.b;
        switch (this.a) {
            case 0:
                int i = LoginButton.y;
                LoginButton this$0 = (LoginButton) obj2;
                q.g(this$0, "this$0");
                if (w1.a.b(this$0) || (rVar = (r) obj) == null) {
                    return;
                }
                try {
                    if (rVar.c && this$0.getVisibility() == 0) {
                        this$0.h(rVar.b);
                        return;
                    }
                    return;
                } catch (Throwable th) {
                    w1.a.a(th, this$0);
                    return;
                }
            case 1:
                ExtensionEmbeddingBackend$SplitListenerWrapper.accept$lambda-1((ExtensionEmbeddingBackend$SplitListenerWrapper) obj2, (ArrayList) obj);
                return;
            case 2:
                Callable callable = (Callable) obj2;
                c6.k kVar = ((k$a) obj).a;
                try {
                    kVar.set(callable.call());
                    return;
                } catch (Exception e) {
                    kVar.setException(e);
                    return;
                }
            case 3:
                x0$b node = (x0$b) obj2;
                q.g(node, "$node");
                x0 this$02 = (x0) obj;
                q.g(this$02, "this$0");
                try {
                    node.a.run();
                    return;
                } finally {
                    this$02.a(node);
                }
            case 4:
                ((GospelCollectManager) obj2).lambda$syncCollectVerseToNet$0((GospelModel) obj);
                return;
            case 5:
                ((f$b) obj2).b((sj1) obj);
                return;
            default:
                final MoreFragment moreFragment = (MoreFragment) obj2;
                final AtomicReference atomicReference = new AtomicReference(Utils.bitmapToBase64((Bitmap) obj));
                TaskService.getInstance().runInMainThread(new Runnable() { // from class: nc.b
                    @Override // java.lang.Runnable
                    public final void run() {
                        MoreFragment moreFragment2 = moreFragment;
                        ia.b bVar = new ia.b("/api/users/%s/", "PUT");
                        bVar.user_id = a1.c().d();
                        bVar.images = (String) atomicReference.get();
                        moreFragment2.b.m(bVar, new f(moreFragment2));
                    }
                });
                return;
        }
    }
}
