package a2;

import android.content.Intent;
import android.graphics.Color;
import android.view.View;
import android.view.View$OnClickListener;
import androidx.constraintlayout.core.motion.utils.TypedValues$TransitionType;
import androidx.fragment.app.FragmentManager;
import com.offline.bible.dao.bible.OneDay;
import com.offline.bible.entity.pray.DxdAudioBean;
import com.offline.bible.entity.pray.topic.ThemePrayModel;
import com.offline.bible.entity.pray.topic.ThemeTopicModel;
import com.offline.bible.ui.EncourageGospelActivity;
import com.offline.bible.ui.aigc.view.WritePrayerActivity;
import com.offline.bible.ui.base.BaseActivity;
import com.offline.bible.ui.dialog.NHMSortBySelectDialog;
import com.offline.bible.ui.dialog.NewShareContentDialog;
import com.offline.bible.ui.dialog.VoiceSelectorDialog;
import com.offline.bible.ui.help.HelpCenterActivity;
import com.offline.bible.ui.home.DxdAudioPlayingActivity;
import com.offline.bible.ui.home.aiverse.ui.AiVerseJarMainActivity;
import com.offline.bible.ui.home.aiverse.ui.VerseOfYouActivity;
import com.offline.bible.ui.home.v7.HomeFragmentV7;
import com.offline.bible.ui.read.ReadFragment$g;
import com.offline.bible.ui.read.bookmarks.BibleBookMarksListActivity;
import com.offline.bible.ui.read.explore.ExploreADialog;
import com.offline.bible.ui.removead.RemoveAdActivity;
import com.offline.bible.ui.search.SearchV2Fragment;
import com.offline.bible.ui.search.theme.ThemePrayDetailActivity;
import com.offline.bible.utils.SPUtil;
import com.offline.bible.utils.Utils;
import com.offline.bible.views.DXDVoiceLayout;
import com.offline.bible.views.businessview.homev5.HomeTaskQuizLayout;
import com.offline.bible.views.businessview.homev5.IHomeTaskQuiz$OnQuizAnswerClick;
import fh.g0;
import kd.n;
import kotlin.jvm.internal.q;
import th.p;
import ua.m6;
import ub.d1;
import ub.w0;
import ya.c$a;
import ya.z0;

/* compiled from: R8$$SyntheticClass */
/* loaded from: classes3.dex */
public final /* synthetic */ class k implements View$OnClickListener {
    public final /* synthetic */ int a;
    public final /* synthetic */ Object b;

    public /* synthetic */ k(Object obj, int i) {
        this.a = i;
        this.b = obj;
    }

    @Override // android.view.View$OnClickListener
    public final void onClick(View view) {
        Object obj = this.b;
        switch (this.a) {
            case 0:
                l this$0 = (l) obj;
                if (w1.a.b(l.class)) {
                    return;
                }
                try {
                    q.g(this$0, "this$0");
                    this$0.a();
                    return;
                } catch (Throwable th) {
                    w1.a.a(th, l.class);
                    return;
                }
            case 1:
                int i = AiVerseJarMainActivity.n;
                AiVerseJarMainActivity context = (AiVerseJarMainActivity) obj;
                q.g(context, "context");
                Intent intent = new Intent(context, (Class<?>) VerseOfYouActivity.class);
                intent.putExtra(TypedValues$TransitionType.S_FROM, "history_type");
                intent.putExtra("mood", (String) null);
                intent.putExtra("message_info", (String) null);
                context.startActivity(intent);
                return;
            case 2:
                ((ExploreADialog) obj).dismiss();
                return;
            case 3:
                int i2 = EncourageGospelActivity.s;
                EncourageGospelActivity encourageGospelActivity = (EncourageGospelActivity) obj;
                m9.c.c().f("GP_encourage_page_Back");
                Intent intent2 = new Intent();
                intent2.putExtra("isAdShowed", encourageGospelActivity.p);
                encourageGospelActivity.setResult(-1, intent2);
                encourageGospelActivity.finish();
                return;
            case 4:
                RemoveAdActivity removeAdActivity = (RemoveAdActivity) obj;
                removeAdActivity.l = 1;
                m6 m6Var = removeAdActivity.j;
                if (m6Var == null) {
                    q.o("viewDataBinding");
                    throw null;
                }
                m6Var.j.setBackground(Utils.getCurrentMode() == 1 ? z0.d(Color.parseColor("#FFEAF6")) : z0.d(Color.parseColor("#36252F")));
                if (removeAdActivity.k()) {
                    m6 m6Var2 = removeAdActivity.j;
                    if (m6Var2 == null) {
                        q.o("viewDataBinding");
                        throw null;
                    }
                    m6Var2.a.setCardBackgroundColor(Color.parseColor("#FFA7D9"));
                } else {
                    m6 m6Var3 = removeAdActivity.j;
                    if (m6Var3 == null) {
                        q.o("viewDataBinding");
                        throw null;
                    }
                    m6Var3.a.setCardBackgroundColor(Color.parseColor("#AB628C"));
                }
                m6 m6Var4 = removeAdActivity.j;
                if (m6Var4 == null) {
                    q.o("viewDataBinding");
                    throw null;
                }
                m6Var4.o.setTextColor(x.e.b(z0.c(), 0.6f));
                m6 m6Var5 = removeAdActivity.j;
                if (m6Var5 == null) {
                    q.o("viewDataBinding");
                    throw null;
                }
                m6Var5.k.setTextColor(x.e.b(z0.b(), 0.6f));
                m6 m6Var6 = removeAdActivity.j;
                if (m6Var6 == null) {
                    q.o("viewDataBinding");
                    throw null;
                }
                m6Var6.n.setVisibility(4);
                m6 m6Var7 = removeAdActivity.j;
                if (m6Var7 == null) {
                    q.o("viewDataBinding");
                    throw null;
                }
                m6Var7.c.setBackground(z0.e(z0.c()));
                m6 m6Var8 = removeAdActivity.j;
                if (m6Var8 == null) {
                    q.o("viewDataBinding");
                    throw null;
                }
                m6Var8.h.setTextColor(z0.c());
                m6 m6Var9 = removeAdActivity.j;
                if (m6Var9 == null) {
                    q.o("viewDataBinding");
                    throw null;
                }
                m6Var9.d.setTextColor(z0.b());
                m6 m6Var10 = removeAdActivity.j;
                if (m6Var10 != null) {
                    m6Var10.g.setVisibility(0);
                    return;
                } else {
                    q.o("viewDataBinding");
                    throw null;
                }
            case 5:
                HomeTaskQuizLayout.lambda$updateQuizLayout$3((IHomeTaskQuiz$OnQuizAnswerClick) obj, view);
                return;
            case 6:
                m9.c.c().h("fp_popup_plantconfirm_click", "action", "cancel");
                ((hc.d) obj).dismiss();
                return;
            case 7:
                BaseActivity context2 = ((SearchV2Fragment) obj).c;
                int i3 = WritePrayerActivity.r;
                q.g(context2, "context");
                Intent intent3 = new Intent(context2, (Class<?>) WritePrayerActivity.class);
                intent3.putExtra("type", 1);
                context2.startActivity(intent3);
                return;
            case 8:
                ThemePrayDetailActivity themePrayDetailActivity = (ThemePrayDetailActivity) obj;
                ThemePrayModel themePrayModel = themePrayDetailActivity.n;
                q.d(themePrayModel);
                NewShareContentDialog newShareContentDialog = new NewShareContentDialog();
                OneDay oneDay = new OneDay();
                oneDay.setContent(themePrayModel.getContent());
                String cardColor = themePrayModel.getCardColor();
                if (cardColor == null) {
                    cardColor = "#D5ECFF";
                }
                oneDay.setTextColor(Color.parseColor(cardColor));
                newShareContentDialog.e = oneDay;
                newShareContentDialog.h = 16;
                newShareContentDialog.show(themePrayDetailActivity.getSupportFragmentManager(), "theme_pray");
                Object obj2 = ya.c.d;
                ya.c cVarA = c$a.a();
                ThemeTopicModel themeTopicModel = themePrayDetailActivity.m;
                q.d(themeTopicModel);
                int i4 = themeTopicModel.get_id();
                ThemePrayModel themePrayModel2 = themePrayDetailActivity.n;
                q.d(themePrayModel2);
                cVarA.g(i4, themePrayModel2.get_id());
                return;
            case 9:
                String str = HomeFragmentV7.p;
                m9.c.c().h("fp_homepage_flowerclick", "home_version", "new");
                dc.c.b(new androidx.room.l((HomeFragmentV7) obj, 5));
                return;
            case 10:
                w0.a((w0) obj);
                return;
            case 11:
                m9.c.c().f("popup_authorize_popup_turnon");
                n nVar = ((d1) obj).c;
                if (nVar != null) {
                    nVar.invoke();
                    return;
                }
                return;
            case 12:
                int i5 = HelpCenterActivity.p;
                ((HelpCenterActivity) obj).finish();
                return;
            case 13:
                int i6 = DxdAudioPlayingActivity.O;
                VoiceSelectorDialog voiceSelectorDialog = new VoiceSelectorDialog();
                final DxdAudioPlayingActivity dxdAudioPlayingActivity = (DxdAudioPlayingActivity) obj;
                voiceSelectorDialog.c = new p() { // from class: xb.b
                    @Override // th.p
                    public final Object invoke(Object obj3, Object obj4) {
                        Integer num = (Integer) obj3;
                        int iIntValue = num.intValue();
                        String url = (String) obj4;
                        int i7 = DxdAudioPlayingActivity.O;
                        kotlin.jvm.internal.q.g(url, "url");
                        DxdAudioPlayingActivity dxdAudioPlayingActivity2 = dxdAudioPlayingActivity;
                        dxdAudioPlayingActivity2.y = iIntValue;
                        SPUtil.getInstant().save(DXDVoiceLayout.KEY_DXD_SELECT_VOICE_INDEX, num);
                        dxdAudioPlayingActivity2.H = true;
                        ie.b bVar = dxdAudioPlayingActivity2.r;
                        dxdAudioPlayingActivity2.F(url, bVar != null ? bVar.getCurrentPosition() : 0, true);
                        return g0.a;
                    }
                };
                FragmentManager supportFragmentManager = dxdAudioPlayingActivity.getSupportFragmentManager();
                q.f(supportFragmentManager, "getSupportFragmentManager(...)");
                int i7 = dxdAudioPlayingActivity.y;
                DxdAudioBean dxdAudioBean = dxdAudioPlayingActivity.u;
                voiceSelectorDialog.g(supportFragmentManager, i7, dxdAudioBean != null ? dxdAudioBean.a() : null);
                return;
            case 14:
                ((ReadFragment$g) obj).b.d.l.setVisibility(8);
                return;
            default:
                int i8 = BibleBookMarksListActivity.p;
                NHMSortBySelectDialog nHMSortBySelectDialog = new NHMSortBySelectDialog();
                BibleBookMarksListActivity bibleBookMarksListActivity = (BibleBookMarksListActivity) obj;
                nHMSortBySelectDialog.b = bibleBookMarksListActivity.n;
                nHMSortBySelectDialog.c = "bookmark";
                nHMSortBySelectDialog.d = new zc.b(bibleBookMarksListActivity);
                FragmentManager supportFragmentManager2 = bibleBookMarksListActivity.getSupportFragmentManager();
                q.f(supportFragmentManager2, "getSupportFragmentManager(...)");
                nHMSortBySelectDialog.g(supportFragmentManager2);
                return;
        }
    }
}
