package a2;

import java.util.Arrays;
import kotlin.jvm.internal.q;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* compiled from: ToolTipPopup.kt */
/* loaded from: classes3.dex */
public final class l$b {
    public static final l$b a;
    public static final /* synthetic */ l$b[] b;

    static {
        l$b l_b = new l$b("BLUE", 0);
        a = l_b;
        b = new l$b[]{l_b, new l$b("BLACK", 1)};
    }

    public l$b() {
        throw null;
    }

    public static l$b valueOf(String value) {
        q.g(value, "value");
        return (l$b) Enum.valueOf(l$b.class, value);
    }

    public static l$b[] values() {
        return (l$b[]) Arrays.copyOf(b, 2);
    }
}
