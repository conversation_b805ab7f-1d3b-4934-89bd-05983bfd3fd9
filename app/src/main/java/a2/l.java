package a2;

import android.app.Activity;
import android.content.Context;
import android.view.View;
import android.view.View$MeasureSpec;
import android.view.ViewTreeObserver;
import android.view.ViewTreeObserver$OnScrollChangedListener;
import android.widget.ImageView;
import android.widget.PopupWindow;
import android.widget.TextView;
import com.bible.holy.bible.for.women.R$id;
import com.facebook.login.widget.LoginButton;
import java.lang.ref.WeakReference;
import kotlin.jvm.internal.q;

/* compiled from: ToolTipPopup.kt */
/* loaded from: classes3.dex */
public final class l {
    public final String a;
    public final WeakReference<View> b;
    public final Context c;
    public l$a d;
    public PopupWindow e;
    public l$b f;
    public long g;
    public final i h;

    /* JADX WARN: Type inference failed for: r1v5, types: [a2.i] */
    public l(String str, LoginButton loginButton) {
        this.a = str;
        this.b = new WeakReference<>(loginButton);
        Context context = loginButton.getContext();
        q.f(context, "anchor.context");
        this.c = context;
        this.f = l$b.a;
        this.g = 6000L;
        this.h = new ViewTreeObserver$OnScrollChangedListener() { // from class: a2.i
            @Override // android.view.ViewTreeObserver$OnScrollChangedListener
            public final void onScrollChanged() {
                PopupWindow popupWindow;
                l this$0 = this.a;
                if (w1.a.b(l.class)) {
                    return;
                }
                try {
                    q.g(this$0, "this$0");
                    if (this$0.b.get() != null && (popupWindow = this$0.e) != null && popupWindow.isShowing()) {
                        if (popupWindow.isAboveAnchor()) {
                            l$a l_a = this$0.d;
                            if (l_a != null) {
                                l_a.a.setVisibility(4);
                                l_a.b.setVisibility(0);
                            }
                        } else {
                            l$a l_a2 = this$0.d;
                            if (l_a2 != null) {
                                l_a2.a.setVisibility(0);
                                l_a2.b.setVisibility(4);
                            }
                        }
                    }
                } catch (Throwable th) {
                    w1.a.a(th, l.class);
                }
            }
        };
    }

    public final void a() {
        if (w1.a.b(this)) {
            return;
        }
        try {
            c();
            PopupWindow popupWindow = this.e;
            if (popupWindow == null) {
                return;
            }
            popupWindow.dismiss();
        } catch (Throwable th) {
            w1.a.a(th, this);
        }
    }

    public final void b() {
        ViewTreeObserver viewTreeObserver;
        Context context = this.c;
        if (w1.a.b(this)) {
            return;
        }
        WeakReference<View> weakReference = this.b;
        try {
            if (weakReference.get() != null) {
                l$a l_a = new l$a(this, context);
                ImageView imageView = l_a.d;
                ImageView imageView2 = l_a.a;
                ImageView imageView3 = l_a.b;
                View view = l_a.c;
                this.d = l_a;
                View viewFindViewById = l_a.findViewById(R$id.lv);
                if (viewFindViewById == null) {
                    throw new NullPointerException("null cannot be cast to non-null type android.widget.TextView");
                }
                ((TextView) viewFindViewById).setText(this.a);
                if (this.f == l$b.a) {
                    view.setBackgroundResource(2131231287);
                    imageView3.setImageResource(2131231288);
                    imageView2.setImageResource(2131231289);
                    imageView.setImageResource(2131231290);
                } else {
                    view.setBackgroundResource(2131231283);
                    imageView3.setImageResource(2131231284);
                    imageView2.setImageResource(2131231285);
                    imageView.setImageResource(2131231286);
                }
                View decorView = ((Activity) context).getWindow().getDecorView();
                q.f(decorView, "window.decorView");
                int width = decorView.getWidth();
                int height = decorView.getHeight();
                if (!w1.a.b(this)) {
                    try {
                        c();
                        View view2 = weakReference.get();
                        if (view2 != null && (viewTreeObserver = view2.getViewTreeObserver()) != null) {
                            viewTreeObserver.addOnScrollChangedListener(this.h);
                        }
                    } catch (Throwable th) {
                        w1.a.a(th, this);
                    }
                }
                l_a.measure(View$MeasureSpec.makeMeasureSpec(width, Integer.MIN_VALUE), View$MeasureSpec.makeMeasureSpec(height, Integer.MIN_VALUE));
                PopupWindow popupWindow = new PopupWindow(l_a, l_a.getMeasuredWidth(), l_a.getMeasuredHeight());
                this.e = popupWindow;
                popupWindow.showAsDropDown(weakReference.get());
                if (!w1.a.b(this)) {
                    try {
                        PopupWindow popupWindow2 = this.e;
                        if (popupWindow2 != null && popupWindow2.isShowing()) {
                            if (popupWindow2.isAboveAnchor()) {
                                l$a l_a2 = this.d;
                                if (l_a2 != null) {
                                    l_a2.a.setVisibility(4);
                                    l_a2.b.setVisibility(0);
                                }
                            } else {
                                l$a l_a3 = this.d;
                                if (l_a3 != null) {
                                    l_a3.a.setVisibility(0);
                                    l_a3.b.setVisibility(4);
                                }
                            }
                        }
                    } catch (Throwable th2) {
                        w1.a.a(th2, this);
                    }
                }
                long j = this.g;
                if (j > 0) {
                    l_a.postDelayed(new j(this, 0), j);
                }
                popupWindow.setTouchable(true);
                l_a.setOnClickListener(new k(this, 0));
            }
        } catch (Throwable th3) {
            w1.a.a(th3, this);
        }
    }

    public final void c() {
        ViewTreeObserver viewTreeObserver;
        if (w1.a.b(this)) {
            return;
        }
        try {
            View view = this.b.get();
            if (view != null && (viewTreeObserver = view.getViewTreeObserver()) != null) {
                viewTreeObserver.removeOnScrollChangedListener(this.h);
            }
        } catch (Throwable th) {
            w1.a.a(th, this);
        }
    }
}
