package a2;

import android.content.DialogInterface;
import android.content.DialogInterface$OnClickListener;
import android.content.SharedPreferences$Editor;
import com.facebook.login.a0;
import com.facebook.login.widget.LoginButton$b;
import e1.h0;
import e1.i$b;
import java.util.Date;
import kotlin.jvm.internal.q;

/* compiled from: R8$$SyntheticClass */
/* loaded from: classes3.dex */
public final /* synthetic */ class f implements DialogInterface$OnClickListener {
    public final /* synthetic */ int a;
    public final /* synthetic */ Object b;

    public /* synthetic */ f(Object obj, int i) {
        this.a = i;
        this.b = obj;
    }

    @Override // android.content.DialogInterface$OnClickListener
    public final void onClick(DialogInterface dialogInterface, int i) {
        Object obj = this.b;
        switch (this.a) {
            case 0:
                a0 loginManager = (a0) obj;
                if (!w1.a.b(LoginButton$b.class)) {
                    try {
                        q.g(loginManager, "$loginManager");
                        Date date = e1.a.l;
                        e1.f.f.a().c(null, true);
                        i$b.a(null);
                        h0.d.a().a(null, true);
                        SharedPreferences$Editor sharedPreferences$EditorEdit = loginManager.c.edit();
                        sharedPreferences$EditorEdit.putBoolean("express_login_allowed", false);
                        sharedPreferences$EditorEdit.apply();
                        break;
                    } catch (Throwable th) {
                        w1.a.a(th, LoginButton$b.class);
                        return;
                    }
                }
                break;
            default:
                com.vungle.ads.internal.presenter.g.showGdpr$lambda-8((com.vungle.ads.internal.presenter.g) obj, dialogInterface, i);
                break;
        }
    }
}
