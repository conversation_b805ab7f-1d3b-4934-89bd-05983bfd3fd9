package a2;

import ads_mobile_sdk.rl1;
import com.facebook.internal.s;
import com.facebook.login.widget.LoginButton;
import com.offline.bible.entity.quiz.QuizItemBean;
import com.offline.bible.ui.quiz.QuizHomeActivity;
import java.lang.reflect.InvocationTargetException;
import kotlin.jvm.internal.q;
import m5.x;
import rf.zo.JUhagZqR;

/* compiled from: R8$$SyntheticClass */
/* loaded from: classes3.dex */
public final /* synthetic */ class c implements Runnable {
    public final /* synthetic */ int a;
    public final /* synthetic */ Object b;
    public final /* synthetic */ Object c;

    public /* synthetic */ c(int i, Object obj, Object obj2) {
        this.a = i;
        this.b = obj;
        this.c = obj2;
    }

    @Override // java.lang.Runnable
    public final void run() throws IllegalAccessException, NoSuchMethodException, SecurityException, IllegalArgumentException, InvocationTargetException {
        int i = 0;
        Object obj = this.c;
        Object obj2 = this.b;
        switch (this.a) {
            case 0:
                int i2 = LoginButton.y;
                LoginButton loginButton = (LoginButton) obj;
                q.g(loginButton, JUhagZqR.psZmiXqu);
                loginButton.getActivity().runOnUiThread(new e(i, loginButton, s.f((String) obj2, false)));
                break;
            case 1:
                rl1.a((rl1) obj, (String) obj2);
                break;
            case 2:
                ((q5.h) obj2).r((x) obj);
                break;
            case 3:
                int i3 = QuizHomeActivity.w;
                QuizHomeActivity quizHomeActivity = (QuizHomeActivity) obj2;
                QuizItemBean quizItemBean = (QuizItemBean) obj;
                quizItemBean.isUnLocked = 1;
                quizHomeActivity.x().i(quizItemBean).c(new tc.j(quizHomeActivity));
                break;
            default:
                u7.f fVar = (u7.f) obj2;
                x7.b bVarB = fVar.b((w7.i) obj);
                if (bVarB != null) {
                    fVar.b.add(bVarB);
                    break;
                }
                break;
        }
    }

    public /* synthetic */ c(rl1 rl1Var, String str) {
        this.a = 1;
        this.c = rl1Var;
        this.b = str;
    }
}
